/**
 * Cart Component with Stripe Payment Integration
 *
 * This component provides cart functionality with integrated Stripe payment processing.
 *
 * Current Implementation:
 * Real backend integration with http://localhost:3000/api/v0/payment/create-payment-intent
 * Proper amount calculation in cents format
 * Error handling and loading states
 * Payment confirmation with backend
 * Full Stripe.js integration with Elements
 *
 * Stripe Integration Structure:
 * - Backend API endpoints configured at /api/v0/payment/
 * - create-payment-intent: Creates payment intent with amount in cents
 * - confirm-payment: Confirms payment after successful processing
 * - create-customer: Creates customer record if needed
 *
 * Key Features:
 * - Secure card input with Stripe Elements
 * - Real-time card validation
 * - Payment processing with stripe.confirmCardPayment()
 * - Complete error handling for payment failures
 *
 * Requirements:
 * - Minimum payment amount: $0.50 (50 cents)
 * - Amount must be sent to backend in cents format
 * - Proper error handling for payment failures
 * - Loading states during payment processing
 */

import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { FaTrash, FaArrowLeft, FaPlus, FaMinus, FaCreditCard, FaSpinner, FaUser, FaEnvelope, FaPhone, FaMapMarkerAlt, FaShieldAlt, FaCheckCircle } from 'react-icons/fa';
import { useCart } from '../../context/CartContext';
import CardInput from './CardInput';
import axios from 'axios'; // Import axios
import { Payment_Url } from '../Url/BaseUrl';

const Cart = () => {
  const { cartItems, cartTotal, removeFromCart, updateQuantity, clearCart } = useCart();

  // State for checkout process
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentError, setPaymentError] = useState('');
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [clientSecret, setClientSecret] = useState('');
  const [showCardInput, setShowCardInput] = useState(false);

  // Debug authentication state and cart data
  useEffect(() => {
    const debugAndFixAuth = () => {
      const authData = {
        userId: localStorage.getItem('userId'),
        userName: localStorage.getItem('userName'),
        userEmail: localStorage.getItem('userEmail'),
        token: localStorage.getItem('token') || localStorage.getItem('isAuthenticated'),
        isAuthenticated: localStorage.getItem('isAuthenticated')
      };

      console.log('🔍 Cart Component - Authentication State:', authData);
      console.log('🔍 Cart Component - Cart Items:', cartItems);
      console.log('🔍 Cart Component - Cart Items Length:', cartItems.length);

      // Auto-fix missing userId by extracting from token
      if (!authData.userId && authData.token) {
        console.warn('⚠️ Warning: Token exists but userId is missing. Attempting automatic fix...');

        try {
          // Decode JWT token to extract userId
          const tokenPayload = JSON.parse(atob(authData.token.split('.')[1]));
          console.log('🔓 Decoded token payload:', tokenPayload);

          if (tokenPayload.id) {
            console.log('✅ Found userId in token, storing it in localStorage');
            localStorage.setItem('userId', tokenPayload.id);

            // Also store token in the standard location if not already there
            if (!localStorage.getItem('token')) {
              localStorage.setItem('token', authData.token);
            }

            console.log('🎉 Authentication data fixed! Order creation should now work.');

            // Show success notification to user
            setAuthFixApplied(true);
            setPaymentError('');

            // Hide the notification after 5 seconds
            setTimeout(() => {
              setAuthFixApplied(false);
            }, 5000);

          } else {
            console.error('❌ Token does not contain user ID. User needs to log out and log back in.');
            setPaymentError('Authentication issue detected. Please log out and log back in to continue.');
          }
        } catch (tokenError) {
          console.error('❌ Failed to decode token:', tokenError);
          setPaymentError('Authentication issue detected. Please log out and log back in to continue.');
        }
      } else if (authData.userId && authData.token) {
        console.log('✅ Authentication state is complete and valid.');
      } else if (!authData.token) {
        console.error('❌ No authentication token found. User is not logged in.');
      }
    };

    debugAndFixAuth();
  }, []);
  const [showUserInfoForm, setShowUserInfoForm] = useState(false);
  const [authFixApplied, setAuthFixApplied] = useState(false);

  // User information state
  const [userInfo, setUserInfo] = useState({
    name: localStorage.getItem('userName') || '',
    email: localStorage.getItem('userEmail') || '',
    phone: '',
    address: ''
  });

  // User info form errors
  const [userInfoErrors, setUserInfoErrors] = useState({});

  // Manual authentication fix function
  const handleAuthFix = () => {
    const token = localStorage.getItem('token') || localStorage.getItem('isAuthenticated');

    if (!token) {
      setPaymentError('No authentication token found. Please log out and log back in.');
      return;
    }

    try {
      const tokenPayload = JSON.parse(atob(token.split('.')[1]));

      if (tokenPayload.id) {
        localStorage.setItem('userId', tokenPayload.id);
        localStorage.setItem('token', token);

        setAuthFixApplied(true);
        setPaymentError('');

        console.log('✅ Manual authentication fix applied successfully');

        setTimeout(() => {
          setAuthFixApplied(false);
        }, 5000);
      } else {
        setPaymentError('Invalid token format. Please log out and log back in.');
      }
    } catch (error) {
      console.error('Manual auth fix failed:', error);
      setPaymentError('Authentication fix failed. Please log out and log back in.');
    }
  };

  // Calculate amount in cents for Stripe (backend requirement)
  const calculateAmountInCents = () => {
    return Math.round(cartTotal * 100);
  };

  // Validate minimum amount ($0.50 as per backend requirement)
  const isValidAmount = () => {
    return cartTotal >= 0.50;
  };

  // Handle user info form input changes
  const handleUserInfoChange = (e) => {
    const { name, value } = e.target;
    setUserInfo(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error for this field if any
    if (userInfoErrors[name]) {
      setUserInfoErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Validate user information
  const validateUserInfo = () => {
    const errors = {};

    if (!userInfo.name.trim()) errors.name = 'Name is required';
    if (!userInfo.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(userInfo.email)) {
      errors.email = 'Email is invalid';
    }
    if (!userInfo.phone.trim()) errors.phone = 'Phone number is required';
    if (!userInfo.address.trim()) errors.address = 'Address is required';

    setUserInfoErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle proceed to payment - collects user info first
  const handleProceedToPayment = () => {
    setPaymentError('');
    setShowUserInfoForm(true);
  };

  // Handle the initial checkout process - after user info validation creates payment intent and shows card form
  const handleCheckout = async () => {
    // Reset previous states
    setPaymentError('');
    setPaymentSuccess(false);

    // Validate user information
    if (!validateUserInfo()) {
      return;
    }

    // Validate cart and amount
    if (cartItems.length === 0) {
      setPaymentError('Your cart is empty');
      return;
    }

    if (!isValidAmount()) {
      setPaymentError('Minimum order amount is $0.50');
      return;
    }

    setIsProcessingPayment(true);

    try {
      // Step 1: Create payment intent with backend using imported URLs
      console.log('Creating payment intent for amount:', calculateAmountInCents(), 'cents');

      const paymentIntentUrl = `${Payment_Url}/create-payment-intent`;
      console.log('Debug: Payment intent URL', paymentIntentUrl);

      const paymentIntentResponse = await fetch(paymentIntentUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ amount: calculateAmountInCents(), currency: 'usd' }),
      });

      if (!paymentIntentResponse.ok) {
        const errorData = await paymentIntentResponse.json().catch(() => ({}));
        console.error('Payment intent response error:', { status: paymentIntentResponse.status, errorData });
        throw new Error(errorData.message || 'Failed to create payment intent');
      }

      const { clientSecret } = await paymentIntentResponse.json();
      console.log('Payment intent created successfully, clientSecret received');

      // Set client secret and show card input
      setClientSecret(clientSecret);
      setShowCardInput(true);
      setIsProcessingPayment(false);

    } catch (error) {
      console.error('Payment error:', error);
      setPaymentError(error.message || 'Failed to initialize payment. Please try again.');
      setIsProcessingPayment(false);
      setShowCardInput(false);
    }
  };

  // Handle successful payment
  const handlePaymentSuccess = async (paymentIntent) => {
    try {
      // Get user info from localStorage with detailed debugging
      const userId = localStorage.getItem('userId');
      const userName = localStorage.getItem('userName');
      const userEmail = localStorage.getItem('userEmail');
      const token = localStorage.getItem('token') || localStorage.getItem('isAuthenticated');

      // Debug authentication state
      console.log('🔍 Authentication Debug:', {
        userId,
        userName,
        userEmail,
        token: token ? 'Present' : 'Missing',
        paymentIntentId: paymentIntent?.id
      });

      // Enhanced authentication check
      if (!userId) {
        console.error('❌ Authentication Error: userId not found in localStorage');
        console.log('📋 Available localStorage keys:', Object.keys(localStorage));

        // Try to extract userId from token if available
        if (token) {
          try {
            // Decode JWT token to get user ID
            const tokenPayload = JSON.parse(atob(token.split('.')[1]));
            console.log('🔓 Token payload:', tokenPayload);

            if (tokenPayload.id) {
              console.log('✅ Found userId in token, using it for order creation');
              // Use the ID from token and store it for future use
              localStorage.setItem('userId', tokenPayload.id);
              // Continue with order creation using token ID
              return await createOrderWithUserData(paymentIntent, tokenPayload.id, userName, userEmail, token);
            }
          } catch (tokenError) {
            console.error('❌ Failed to decode token:', tokenError);
          }
        }

        setPaymentError('Authentication error: Please log out and log back in to complete your order.');
        return;
      }

      // Continue with normal order creation
      return await createOrderWithUserData(paymentIntent, userId, userName, userEmail, token);

    } catch (error) {
      console.error('❌ Critical error in payment success handler:', error);
      setPaymentSuccess(true);
      clearCart();
      setPaymentError('Payment successful, but there was an issue creating your order record. Please contact support with your payment confirmation.');
    }
  };

  // Validate cart items before order creation
  const validateCartItems = () => {
    console.log('🔍 Cart: Validating cart items...');
    console.log('🔍 Cart: Current cartItems:', cartItems);

    const issues = [];

    if (!cartItems || cartItems.length === 0) {
      issues.push('Cart is empty');
      console.error('❌ Cart: Cart is empty');
      return issues;
    }

    cartItems.forEach((item, index) => {
      console.log(`🔍 Cart: Validating item ${index + 1}:`, item);

      if (!item.id) {
        issues.push(`Item ${index + 1}: Missing product ID`);
        console.error(`❌ Cart: Item ${index + 1} missing ID:`, item);
      }
      if (!item.title) {
        issues.push(`Item ${index + 1}: Missing product title`);
        console.error(`❌ Cart: Item ${index + 1} missing title:`, item);
      }
      if (!item.price) {
        issues.push(`Item ${index + 1}: Missing product price`);
        console.error(`❌ Cart: Item ${index + 1} missing price:`, item);
      }
      if (!item.quantity || item.quantity <= 0) {
        issues.push(`Item ${index + 1}: Invalid quantity`);
        console.error(`❌ Cart: Item ${index + 1} invalid quantity:`, item);
      }
    });

    if (issues.length > 0) {
      console.warn('⚠️ Cart validation issues:', issues);
      console.warn('⚠️ Cart validation failed items:', cartItems);
    } else {
      console.log('✅ Cart items validation passed');
    }

    return issues;
  };

  // Extracted order creation function for better error handling
  const createOrderWithUserData = async (paymentIntent, userId, userName, userEmail, token) => {
    try {
      // Validate required shipping information
      if (!userInfo.name || !userInfo.email || !userInfo.phone || !userInfo.address) {
        throw new Error('Missing required shipping information');
      }

      // Validate cart items
      const cartIssues = validateCartItems();
      if (cartIssues.length > 0) {
        throw new Error(`Cart validation failed: ${cartIssues.join(', ')}`);
      }

      // Prepare order data with enhanced product information
      const orderData = {
        user: userId,
        products: cartItems.map(item => {
          // Extract price as number (remove $ sign)
          const priceNum = parseFloat(item.price.replace('$', ''));
          const priceInCents = Math.round(priceNum * 100);

          return {
            product: item.id,
            quantity: item.quantity,
            // Include additional product info for mock products
            name: item.title,
            price: priceInCents // Price in cents
          };
        }),
        amount: calculateAmountInCents(),
        paymentIntentId: paymentIntent.id,
        paymentStatus: 'succeeded',
        customerInfo: {
          name: userInfo.name || userName,
          email: userInfo.email || userEmail,
          phone: userInfo.phone
        },
        shippingAddress: {
          address: userInfo.address,
          city: 'Default City', // You can enhance this to collect city separately
          postalCode: '00000', // You can enhance this to collect postal code separately
          country: 'US'
        }
      };

      console.log('📦 Creating order with data:', orderData);
      console.log('📦 Cart items being processed:', cartItems.map(item => ({
        id: item.id,
        idType: typeof item.id,
        title: item.title,
        price: item.price,
        quantity: item.quantity
      })));

      // Additional validation before sending to backend
      const hasInvalidProducts = cartItems.some(item => !item.id || !item.title || !item.price);
      if (hasInvalidProducts) {
        console.error('❌ Invalid products detected in cart:', cartItems);
        throw new Error('Cart contains invalid product data');
      }

      console.log('✅ Cart validation passed, sending to backend...');

      // Create order in backend
      const orderResponse = await axios.post('http://localhost:3000/api/v0/orders/create', orderData, {
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` })
        }
      });

      console.log('📋 Order creation response:', orderResponse.data);

      if (orderResponse.data.success) {
        console.log('✅ Order created successfully:', orderResponse.data.order);

        // Check if order contains mock products
        if (orderResponse.data.metadata?.hasMockProducts) {
          console.log('ℹ️ Order contains mock products:', orderResponse.data.metadata.mockProducts);
        }

        setPaymentSuccess(true);
        clearCart();  // Clear cart after successful payment
        setPaymentError('');

        // Store order info for confirmation display
        localStorage.setItem('lastOrderNumber', orderResponse.data.order.orderNumber);

        // Store additional order metadata
        if (orderResponse.data.metadata) {
          localStorage.setItem('lastOrderMetadata', JSON.stringify(orderResponse.data.metadata));
        }

        console.log('🎉 Order process completed successfully!');
      } else {
        throw new Error(orderResponse.data.message || 'Failed to create order');
      }

    } catch (error) {
      console.error('❌ Error creating order:', error);

      // Provide specific error messages
      if (error.response) {
        const errorMsg = error.response.data?.message || 'Server error occurred';
        const errorDetails = error.response.data?.error || '';
        console.error('❌ Server response error:', error.response.data);
        console.error('❌ HTTP Status:', error.response.status);

        // Check for specific MongoDB ObjectId error
        if (errorDetails.includes('Cast to ObjectId failed')) {
          console.error('❌ Product ID format error detected');
          setPaymentError('Payment successful, but there was a product ID format issue. Our team has been notified and will process your order manually. Please contact support with your payment confirmation.');
        } else {
          setPaymentError(`Payment successful, but order creation failed: ${errorMsg}. Please contact support.`);
        }
      } else if (error.request) {
        console.error('❌ Network error:', error.request);
        setPaymentError('Payment successful, but unable to connect to server. Please contact support.');
      } else {
        console.error('❌ Order creation error:', error.message);
        setPaymentError(`Payment successful, but order creation failed: ${error.message}. Please contact support.`);
      }

      // Still show success since payment went through
      setPaymentSuccess(true);
      clearCart();
    }
  };

  // Handle payment error
  const handlePaymentError = (errorMessage) => {
    setPaymentError(errorMessage || 'Payment failed. Please try again.');
    setIsProcessingPayment(false);
  };

  // Return empty cart or success message if no items
  if (cartItems.length === 0 || paymentSuccess) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          {paymentSuccess ? (
            <div className="py-8">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-10 h-10 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">Order Placed Successfully!</h2>
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                {localStorage.getItem('lastOrderNumber') && (
                  <p className="text-gray-700 mb-2">
                    <span className="font-semibold">Order Number:</span> {localStorage.getItem('lastOrderNumber')}
                  </p>
                )}
                <p className="text-gray-600 text-sm">
                  You will receive an email confirmation shortly. You can track your order status in your order history.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Link
                  to="/orders"
                  className="inline-flex items-center justify-center px-4 py-2 bg-[#575CEE] text-white rounded-lg hover:bg-[#4a4fd1] transition-colors"
                >
                  View Order History
                </Link>
                <Link
                  to="/home"
                  className="inline-flex items-center justify-center px-4 py-2 text-[#575CEE] border border-[#575CEE] rounded-lg hover:bg-[#575CEE] hover:text-white transition-colors"
                >
                  <FaArrowLeft className="mr-2" />
                  Continue Shopping
                </Link>
              </div>
            </div>
          ) : (
            <div className="py-8">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">Your cart is empty</h2>
              <p className="text-gray-600 mb-6">Looks like you haven't added any items to your cart yet.</p>
              <Link to="/home" className="inline-flex items-center text-[#575CEE] hover:text-[#4a4fd1]">
                <FaArrowLeft className="mr-2" />
                Start Shopping
              </Link>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto bg-white rounded-lg shadow-md p-6">
        {/* Authentication Fix Notification */}
        {authFixApplied && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800">Authentication Fixed!</h3>
                <p className="text-sm text-green-700 mt-1">
                  Your account has been automatically updated. You can now complete your order without any issues.
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Your Cart</h1>
          <button
            onClick={clearCart}
            className="text-red-500 hover:text-red-700 flex items-center"
          >
            <FaTrash className="mr-1" /> Clear Cart
          </button>
        </div>

        {/* Side-by-side layout for checkout process */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Left side - Cart items */}
          <div className="lg:w-1/2">
            <h2 className="text-lg font-medium mb-4">Items in Your Cart</h2>
            <div className="border-t border-gray-200 pt-4">
              {cartItems.map((item) => (
                <div key={item.id} className="flex flex-col sm:flex-row items-center py-4 border-b border-gray-200">
                  <div className="w-20 h-20 flex-shrink-0 bg-gray-100 rounded-md overflow-hidden mr-4 mb-4 sm:mb-0">
                    <img
                      src={item.img}
                      alt={item.title}
                      className="w-full h-full object-contain p-2"
                    />
                  </div>

                  <div className="flex-grow">
                    <h3 className="font-medium text-gray-900">{item.title}</h3>
                    <p className="text-[#575CEE] font-bold mt-1">{item.price}</p>
                  </div>

                  <div className="flex items-center mt-4 sm:mt-0">
                    <button
                      onClick={() => updateQuantity(item.id, item.quantity - 1)}
                      className="p-1 rounded-full bg-gray-200 hover:bg-gray-300"
                    >
                      <FaMinus className="text-gray-600" />
                    </button>

                    <span className="mx-3 w-6 text-center">{item.quantity}</span>

                    <button
                      onClick={() => updateQuantity(item.id, item.quantity + 1)}
                      className="p-1 rounded-full bg-gray-200 hover:bg-gray-300"
                    >
                      <FaPlus className="text-gray-600" />
                    </button>

                    <button
                      onClick={() => removeFromCart(item.id)}
                      className="ml-3 text-red-500 hover:text-red-700"
                    >
                      <FaTrash />
                    </button>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-4">
              <div className="flex justify-between text-lg font-medium">
                <span>Subtotal</span>
                <span>${cartTotal.toFixed(2)}</span>
              </div>

              {/* Amount validation message */}
              {cartTotal > 0 && cartTotal < 0.50 && (
                <p className="text-amber-600 text-sm mt-1">
                  Minimum order amount is $0.50
                </p>
              )}
            </div>
          </div>

          {/* Right side - User info form or payment */}
          <div className="lg:w-1/2 border-t lg:border-t-0 lg:border-l border-gray-200 lg:pl-8 pt-4 lg:pt-0">
            {/* Progress Indicator */}
            {(showUserInfoForm || showCardInput) && (
              <div className="mb-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`flex items-center justify-center w-8 h-8 rounded-full ${showUserInfoForm ? 'bg-[#575CEE] text-white' : 'bg-gray-200 text-gray-600'}`}>
                      {showCardInput ? <FaCheckCircle className="h-4 w-4" /> : '1'}
                    </div>
                    <span className={`ml-2 text-sm font-medium ${showUserInfoForm ? 'text-[#575CEE]' : 'text-gray-500'}`}>
                      Shipping Info
                    </span>
                  </div>
                  <div className={`flex-1 h-0.5 mx-4 ${showCardInput ? 'bg-[#575CEE]' : 'bg-gray-200'}`}></div>
                  <div className="flex items-center">
                    <div className={`flex items-center justify-center w-8 h-8 rounded-full ${showCardInput ? 'bg-[#575CEE] text-white' : 'bg-gray-200 text-gray-600'}`}>
                      2
                    </div>
                    <span className={`ml-2 text-sm font-medium ${showCardInput ? 'text-[#575CEE]' : 'text-gray-500'}`}>
                      Payment
                    </span>
                  </div>
                </div>
              </div>
            )}

            {!showUserInfoForm && !showCardInput && (
              <div className="text-center py-8">
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 mb-6">
                  <div className="flex items-center justify-center w-16 h-16 bg-[#575CEE] rounded-full mx-auto mb-4">
                    <FaShieldAlt className="text-white text-2xl" />
                  </div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-2">Ready to Complete Your Order?</h2>
                  <p className="text-gray-600 mb-4">Secure checkout with encrypted payment processing</p>
                </div>
                <button
                  onClick={handleProceedToPayment}
                  disabled={cartTotal < 0.50 || cartItems.length === 0}
                  className={`w-full flex items-center justify-center py-4 px-6 rounded-lg font-semibold transition-all duration-200 mb-4 ${
                    cartTotal < 0.50 || cartItems.length === 0
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-gradient-to-r from-[#575CEE] to-[#4a4fd1] text-white hover:from-[#4a4fd1] hover:to-[#3d42c4] shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'
                  }`}
                >
                  <FaCreditCard className="mr-2" />
                  Proceed to Checkout
                </button>
                {cartTotal < 0.50 && (
                  <p className="text-sm text-red-600">Minimum order amount is $0.50</p>
                )}
              </div>
            )}

            {/* User Information Form */}
            {showUserInfoForm && !showCardInput && (
              <div className="mt-6 bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                {/* Header Section */}
                <div className="bg-gradient-to-r from-[#575CEE] to-[#4a4fd1] px-6 py-4">
                  <div className="flex items-center">
                    <div className="bg-white/20 p-2 rounded-lg mr-3">
                      <FaUser className="text-white text-lg" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-white">Shipping Information</h3>
                      <p className="text-white/80 text-sm">Please provide your details for delivery</p>
                    </div>
                  </div>
                </div>

                {/* Form Content */}
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Full Name */}
                    <div className="md:col-span-2">
                      <label htmlFor="name" className="block text-sm font-semibold text-gray-700 mb-2">
                        Full Name <span className="text-red-500">*</span>
                      </label>
                      <div className="relative group">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <FaUser className={`h-4 w-4 transition-colors ${userInfoErrors.name ? 'text-red-400' : 'text-gray-400 group-focus-within:text-[#575CEE]'}`} />
                        </div>
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={userInfo.name}
                          onChange={handleUserInfoChange}
                          placeholder="Enter your full name"
                          className={`block w-full pl-10 pr-4 py-3 border-2 rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-0 ${
                            userInfoErrors.name
                              ? 'border-red-300 bg-red-50 focus:border-red-500'
                              : 'border-gray-200 bg-white focus:border-[#575CEE] focus:bg-blue-50/30'
                          } text-gray-900 placeholder-gray-500`}
                        />
                        {userInfoErrors.name && (
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <svg className="h-4 w-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                          </div>
                        )}
                      </div>
                      {userInfoErrors.name && (
                        <p className="mt-2 text-sm text-red-600 flex items-center">
                          <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          {userInfoErrors.name}
                        </p>
                      )}
                    </div>

                    {/* Email Address */}
                    <div>
                      <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-2">
                        Email Address <span className="text-red-500">*</span>
                      </label>
                      <div className="relative group">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <FaEnvelope className={`h-4 w-4 transition-colors ${userInfoErrors.email ? 'text-red-400' : 'text-gray-400 group-focus-within:text-[#575CEE]'}`} />
                        </div>
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={userInfo.email}
                          onChange={handleUserInfoChange}
                          placeholder="<EMAIL>"
                          className={`block w-full pl-10 pr-4 py-3 border-2 rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-0 ${
                            userInfoErrors.email
                              ? 'border-red-300 bg-red-50 focus:border-red-500'
                              : 'border-gray-200 bg-white focus:border-[#575CEE] focus:bg-blue-50/30'
                          } text-gray-900 placeholder-gray-500`}
                        />
                        {userInfoErrors.email && (
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <svg className="h-4 w-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                          </div>
                        )}
                      </div>
                      {userInfoErrors.email && (
                        <p className="mt-2 text-sm text-red-600 flex items-center">
                          <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          {userInfoErrors.email}
                        </p>
                      )}
                    </div>

                    {/* Phone Number */}
                    <div>
                      <label htmlFor="phone" className="block text-sm font-semibold text-gray-700 mb-2">
                        Phone Number <span className="text-red-500">*</span>
                      </label>
                      <div className="relative group">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <FaPhone className={`h-4 w-4 transition-colors ${userInfoErrors.phone ? 'text-red-400' : 'text-gray-400 group-focus-within:text-[#575CEE]'}`} />
                        </div>
                        <input
                          type="tel"
                          id="phone"
                          name="phone"
                          value={userInfo.phone}
                          onChange={handleUserInfoChange}
                          placeholder="+****************"
                          className={`block w-full pl-10 pr-4 py-3 border-2 rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-0 ${
                            userInfoErrors.phone
                              ? 'border-red-300 bg-red-50 focus:border-red-500'
                              : 'border-gray-200 bg-white focus:border-[#575CEE] focus:bg-blue-50/30'
                          } text-gray-900 placeholder-gray-500`}
                        />
                        {userInfoErrors.phone && (
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <svg className="h-4 w-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                          </div>
                        )}
                      </div>
                      {userInfoErrors.phone && (
                        <p className="mt-2 text-sm text-red-600 flex items-center">
                          <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          {userInfoErrors.phone}
                        </p>
                      )}
                    </div>

                    {/* Address */}
                    <div className="md:col-span-2">
                      <label htmlFor="address" className="block text-sm font-semibold text-gray-700 mb-2">
                        Shipping Address <span className="text-red-500">*</span>
                      </label>
                      <div className="relative group">
                        <div className="absolute top-3 left-3 pointer-events-none">
                          <FaMapMarkerAlt className={`h-4 w-4 transition-colors ${userInfoErrors.address ? 'text-red-400' : 'text-gray-400 group-focus-within:text-[#575CEE]'}`} />
                        </div>
                        <textarea
                          id="address"
                          name="address"
                          rows="3"
                          value={userInfo.address}
                          onChange={handleUserInfoChange}
                          placeholder="Enter your complete shipping address"
                          className={`block w-full pl-10 pr-4 py-3 border-2 rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-0 resize-none ${
                            userInfoErrors.address
                              ? 'border-red-300 bg-red-50 focus:border-red-500'
                              : 'border-gray-200 bg-white focus:border-[#575CEE] focus:bg-blue-50/30'
                          } text-gray-900 placeholder-gray-500`}
                        />
                        {userInfoErrors.address && (
                          <div className="absolute top-3 right-3 pointer-events-none">
                            <svg className="h-4 w-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                          </div>
                        )}
                      </div>
                      {userInfoErrors.address && (
                        <p className="mt-2 text-sm text-red-600 flex items-center">
                          <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          {userInfoErrors.address}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Security Notice */}
                  <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h4 className="text-sm font-medium text-blue-800">Secure Checkout</h4>
                        <p className="text-sm text-blue-700 mt-1">
                          Your information is encrypted and secure. We never store your payment details.
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="mt-6 flex flex-col sm:flex-row gap-3">
                    <button
                      onClick={() => setShowUserInfoForm(false)}
                      className="flex-1 bg-gray-100 text-gray-700 py-3 px-6 rounded-lg font-medium hover:bg-gray-200 transition-colors duration-200 flex items-center justify-center border border-gray-300"
                    >
                      <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                      </svg>
                      Back to Cart
                    </button>
                    <button
                      onClick={handleCheckout}
                      disabled={isProcessingPayment}
                      className="flex-1 bg-gradient-to-r from-[#575CEE] to-[#4a4fd1] text-white py-3 px-6 rounded-lg font-medium hover:from-[#4a4fd1] hover:to-[#3d42c4] disabled:from-gray-300 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    >
                      {isProcessingPayment ? (
                        <>
                          <FaSpinner className="animate-spin mr-2 h-4 w-4" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <FaCreditCard className="mr-2 h-4 w-4" />
                          Continue to Payment
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Card Input Component */}
            {showCardInput && clientSecret && (
              <div>
                <h2 className="text-lg font-medium mb-4">Payment Details</h2>
                <CardInput
                  clientSecret={clientSecret}
                  onPaymentSuccess={handlePaymentSuccess}
                  onPaymentError={handlePaymentError}
                />
              </div>
            )}

            {/* Payment Messages */}
            {paymentError && (
              <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3 flex-1">
                    <p className="text-red-600 text-sm">{paymentError}</p>

                    {/* Show fix button for authentication issues */}
                    {paymentError.includes('Authentication') && (
                      <div className="mt-3 flex flex-col sm:flex-row gap-2">
                        <button
                          onClick={handleAuthFix}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-[#575CEE] hover:bg-[#4a4fd1] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#575CEE]"
                        >
                          <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                          </svg>
                          Fix Authentication
                        </button>
                        <button
                          onClick={() => {
                            localStorage.clear();
                            window.location.href = '/login';
                          }}
                          className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#575CEE]"
                        >
                          <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                          </svg>
                          Log Out & Re-login
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {paymentSuccess && (
              <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-green-600 text-sm">
                  Payment successful!
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Back to Shopping Button */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <Link
            to="/home"
            className="w-full block text-center border border-gray-300 text-gray-700 py-3 px-4 rounded-md hover:bg-gray-50 transition-colors"
          >
            <FaArrowLeft className="inline mr-2" /> Continue Shopping
          </Link>
        </div>
      </div>

    </div>
  );
};

export default Cart;

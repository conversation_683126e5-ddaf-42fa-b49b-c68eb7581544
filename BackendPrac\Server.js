require('dotenv').config();
const exp = require('express');
const app = exp();
const cors = require('cors')
const ConnectDb = require('./config/Db');
const  Route = require('./route/rawDataRoute');
const paymentRoute = require('./route/paymentRoute');
const productRoute = require('./route/productRoutes');
const orderRoute = require('./route/orderRoutes');
const path = require('path');

// app.get('/', (req, res) => {
//     res.send('Hello World!');
// });

app.use(cors());
app.use(exp.json());
app.use('/api/v0/data',Route);
app.use('/api/v0/payment', paymentRoute);
app.use('/api/v0/product', productRoute);
app.use('/api/v0/orders', orderRoute);
app.use('/images', exp.static(path.join(__dirname, 'tmp/images')));
app.listen(3000, () => {
    ConnectDb();
    console.log('Server started');
});
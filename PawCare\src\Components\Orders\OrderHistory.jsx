import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { FaSearch, FaEye, FaDownload, FaShoppingCart, FaCalendarAlt, FaBox, FaTruck, FaCheckCircle } from 'react-icons/fa';

const OrderHistory = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedOrder, setSelectedOrder] = useState(null);

  useEffect(() => {
    fetchUserOrders();
  }, []);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchUserOrders();
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, statusFilter]);

  const fetchUserOrders = async () => {
    setLoading(true);
    try {
      const userId = localStorage.getItem('userId');
      if (!userId) {
        setError('Please log in to view your orders');
        setLoading(false);
        return;
      }

      const response = await axios.get(`http://localhost:3000/api/v0/orders/user/${userId}`, {
        params: {
          page: 1,
          limit: 20,
          status: statusFilter !== 'all' ? statusFilter : undefined
        },
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.data.success) {
        setOrders(response.data.orders);
        setError(null);
      } else {
        throw new Error('Failed to fetch orders');
      }
    } catch (err) {
      console.error('Error fetching user orders:', err);
      // Mock data for demo
      const mockOrders = [
        {
          _id: '1',
          orderNumber: 'PW123456',
          amount: 2500,
          status: 'delivered',
          createdAt: new Date(Date.now() - 86400000 * 3).toISOString(),
          products: [
            { product: { name: 'Premium Dog Food', price: 1500 }, quantity: 1 },
            { product: { name: 'Cat Toy Set', price: 1000 }, quantity: 1 }
          ],
          trackingNumber: 'TRK123456789'
        },
        {
          _id: '2',
          orderNumber: 'PW123457',
          amount: 1800,
          status: 'shipped',
          createdAt: new Date(Date.now() - 86400000).toISOString(),
          products: [
            { product: { name: 'Pet Bed', price: 1800 }, quantity: 1 }
          ],
          trackingNumber: 'TRK987654321'
        }
      ];
      setOrders(mockOrders);
      setError('Using demo data - Please log in to see real orders');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <FaCalendarAlt className="text-yellow-500" />;
      case 'processing':
        return <FaBox className="text-blue-500" />;
      case 'shipped':
        return <FaTruck className="text-purple-500" />;
      case 'delivered':
      case 'completed':
        return <FaCheckCircle className="text-green-500" />;
      default:
        return <FaBox className="text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'canceled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredOrders = orders.filter(order => {
    const matchesSearch =
      order.orderNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.products?.some(item =>
        item.product?.name?.toLowerCase().includes(searchTerm.toLowerCase())
      );

    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const reorderItems = (order) => {
    // This would integrate with your cart context to add items back to cart
    console.log('Reordering items:', order.products);
    // Implementation would depend on your cart context structure
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#575CEE]"></div>
          <span className="ml-3 text-lg">Loading your orders...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Order History</h1>
          <p className="text-gray-600">Track and manage your PawCare orders</p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-yellow-800">{error}</p>
          </div>
        )}

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search by order number or product name..."
                className="pl-10 pr-4 py-2 w-full border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <select
              className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Orders</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="completed">Completed</option>
            </select>
          </div>
        </div>

        {/* Orders List */}
        {filteredOrders.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm p-12 text-center">
            <FaShoppingCart className="mx-auto h-16 w-16 text-gray-400 mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No Orders Found</h3>
            <p className="text-gray-600 mb-6">
              {searchTerm || statusFilter !== 'all'
                ? 'No orders match your current filters.'
                : "You haven't placed any orders yet."}
            </p>
            <Link
              to="/home"
              className="inline-flex items-center px-4 py-2 bg-[#575CEE] text-white rounded-lg hover:bg-[#4a4fd1] transition-colors"
            >
              <FaShoppingCart className="mr-2" />
              Start Shopping
            </Link>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredOrders.map((order) => (
              <div key={order._id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div className="p-6">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 mr-3">
                          Order #{order.orderNumber}
                        </h3>
                        <div className="flex items-center">
                          {getStatusIcon(order.status)}
                          <span className={`ml-2 px-2 py-1 text-xs rounded-full font-medium ${getStatusColor(order.status)}`}>
                            {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                          </span>
                        </div>
                      </div>

                      <div className="text-sm text-gray-600 mb-3">
                        <p>Placed on {new Date(order.createdAt).toLocaleDateString()}</p>
                        <p className="font-semibold text-gray-900">Total: ${(order.amount / 100).toFixed(2)}</p>
                        {order.trackingNumber && (
                          <p>Tracking: <span className="font-mono">{order.trackingNumber}</span></p>
                        )}
                      </div>

                      <div className="flex flex-wrap gap-2 mb-4">
                        {order.products?.slice(0, 3).map((item, index) => (
                          <span key={index} className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                            {item.name || item.product?.name || 'Unknown Product'} (x{item.quantity})
                          </span>
                        ))}
                        {order.products?.length > 3 && (
                          <span className="text-xs text-gray-500">
                            +{order.products.length - 3} more items
                          </span>
                        )}
                      </div>

                      {/* Shipping Address Preview */}
                      {order.shippingAddress && (
                        <div className="text-xs text-gray-600 mb-2">
                          <span className="font-medium">Shipped to:</span> {order.shippingAddress.city}, {order.shippingAddress.country}
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col sm:flex-row gap-2 lg:ml-6">
                      <button
                        onClick={() => setSelectedOrder(order)}
                        className="flex items-center justify-center px-4 py-2 text-[#575CEE] border border-[#575CEE] rounded-lg hover:bg-[#575CEE] hover:text-white transition-colors"
                      >
                        <FaEye className="mr-2" />
                        View Details
                      </button>

                      {(order.status === 'delivered' || order.status === 'completed') && (
                        <button
                          onClick={() => reorderItems(order)}
                          className="flex items-center justify-center px-4 py-2 bg-[#575CEE] text-white rounded-lg hover:bg-[#4a4fd1] transition-colors"
                        >
                          <FaShoppingCart className="mr-2" />
                          Reorder
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Order Details Modal */}
        {selectedOrder && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-semibold">Order Details</h2>
                  <button
                    onClick={() => setSelectedOrder(null)}
                    className="text-gray-400 hover:text-gray-600 text-2xl"
                  >
                    ×
                  </button>
                </div>

                <div className="space-y-6">
                  {/* Order Info */}
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-3">Order Information</h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">Order Number</p>
                        <p className="font-medium">{selectedOrder.orderNumber}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Status</p>
                        <div className="flex items-center">
                          {getStatusIcon(selectedOrder.status)}
                          <span className={`ml-2 px-2 py-1 text-xs rounded-full font-medium ${getStatusColor(selectedOrder.status)}`}>
                            {selectedOrder.status.charAt(0).toUpperCase() + selectedOrder.status.slice(1)}
                          </span>
                        </div>
                      </div>
                      <div>
                        <p className="text-gray-600">Order Date</p>
                        <p className="font-medium">{new Date(selectedOrder.createdAt).toLocaleDateString()}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Total Amount</p>
                        <p className="font-medium">${(selectedOrder.amount / 100).toFixed(2)}</p>
                      </div>
                    </div>
                  </div>

                  {/* Shipping Address */}
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-3">Shipping Address</h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      {selectedOrder.shippingAddress ? (
                        <div className="text-sm space-y-1">
                          <p className="font-medium">{selectedOrder.shippingAddress.address}</p>
                          <p>{selectedOrder.shippingAddress.city}, {selectedOrder.shippingAddress.postalCode}</p>
                          <p>{selectedOrder.shippingAddress.country}</p>
                        </div>
                      ) : (
                        <p className="text-gray-500 text-sm">No shipping address available</p>
                      )}
                    </div>
                  </div>

                  {/* Products */}
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-3">Items Ordered</h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="space-y-3">
                        {selectedOrder.products?.map((item, index) => (
                          <div key={index} className="flex justify-between items-center py-3 px-3 bg-white rounded border">
                            <div className="flex-1">
                              <p className="font-medium text-gray-900">
                                {item.name || item.product?.name || 'Unknown Product'}
                              </p>
                              <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                                <span>Quantity: {item.quantity}</span>
                                <span>Unit Price: ${((item.price || item.product?.price || 0) / 100).toFixed(2)}</span>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-semibold text-gray-900">
                                ${(((item.price || item.product?.price || 0) * item.quantity) / 100).toFixed(2)}
                              </p>
                            </div>
                          </div>
                        ))}

                        {/* Order Total */}
                        <div className="border-t pt-3 mt-3">
                          <div className="flex justify-between items-center">
                            <span className="font-semibold text-gray-900">Total Amount:</span>
                            <span className="font-bold text-lg text-[#575CEE]">
                              ${(selectedOrder.amount / 100).toFixed(2)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Tracking */}
                  {selectedOrder.trackingNumber && (
                    <div>
                      <h3 className="font-semibold text-gray-800 mb-3">Tracking Information</h3>
                      <p className="text-sm text-gray-600">Tracking Number</p>
                      <p className="font-mono text-lg">{selectedOrder.trackingNumber}</p>
                    </div>
                  )}
                </div>

                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    onClick={() => setSelectedOrder(null)}
                    className="px-4 py-2 text-gray-600 border rounded-lg hover:bg-gray-50"
                  >
                    Close
                  </button>
                  <button className="px-4 py-2 bg-[#575CEE] text-white rounded-lg hover:bg-[#4a4fd1] flex items-center">
                    <FaDownload className="mr-2" />
                    Download Receipt
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderHistory;

const express = require('express');
const router = express.Router();
const upload = require('../Middleware/Multer');
const {
    createProduct,
    getAllProducts,
    getProductById,
    updateProduct,
    deleteProduct,
    getProductStats
} = require('../controller/rawDataController');

// Test route for multer debugging
router.post('/test-upload', upload.single('image'), (req, res) => {
    console.log('🧪 === MULTER TEST DEBUG START ===');
    console.log('📋 Request body:', req.body);
    console.log('📷 Request file:', req.file);
    console.log('📂 Request headers:', req.headers);

    if (req.file) {
        console.log('✅ Multer working! File received:', {
            originalname: req.file.originalname,
            filename: req.file.filename,
            mimetype: req.file.mimetype,
            size: req.file.size,
            destination: req.file.destination,
            path: req.file.path
        });

        res.json({
            success: true,
            message: 'File upload test successful',
            file: req.file,
            body: req.body
        });
    } else {
        console.log('❌ Multer not working - no file received');
        res.json({
            success: false,
            message: 'No file received',
            body: req.body
        });
    }
    console.log('🏁 === MULTER TEST DEBUG END ===');
});

// Product CRUD Routes
router.post('/create', upload.single('image'), createProduct);           // Create product
router.get('/all', getAllProducts);                                      // Get all products (with filters)
router.get('/stats', getProductStats);                                   // Get product statistics
router.get('/:productId', getProductById);                               // Get single product
router.put('/:productId', upload.single('image'), updateProduct);       // Update product
router.delete('/:productId', deleteProduct);                            // Delete product (soft delete)

module.exports = router;
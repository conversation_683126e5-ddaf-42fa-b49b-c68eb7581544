import React, { useState, useEffect } from 'react';
import { FaSearch, FaEye, FaEdit, FaTrash, FaFilter, FaPlus, FaBox, FaExclamationTriangle } from 'react-icons/fa';
import { productService, PRODUCT_CATEGORIES, PRODUCT_STATUS_OPTIONS } from '../../../services/productService';

const ProductsSection = () => {
  const [products, setProducts] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [editingProduct, setEditingProduct] = useState(null);
  const [notification, setNotification] = useState({ type: '', message: '' });
  const [deleteConfirm, setDeleteConfirm] = useState(null);

  useEffect(() => {
    fetchProducts();
  }, []);

  // Refetch products when filters change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchProducts();
    }, 500); // Debounce search

    return () => clearTimeout(timeoutId);
  }, [searchTerm, categoryFilter, statusFilter]);

  const fetchProducts = async () => {
    setLoading(true);
    try {
      const params = {
        page: 1,
        limit: 50,
        category: categoryFilter !== 'all' ? categoryFilter : undefined,
        search: searchTerm || undefined,
        status: statusFilter !== 'all' ? statusFilter : undefined,
        includeDeleted: statusFilter === 'deleted' ? 'true' : 'false'
      };

      const response = await productService.getAllProducts(params);

      if (response.success) {
        setProducts(response.products);
        setError(null);
      } else {
        throw new Error('Failed to fetch products');
      }
    } catch (err) {
      console.error('Error fetching products:', err);
      // Fallback to mock data if API fails
      const mockProducts = [
        {
          _id: '1',
          name: 'Premium Dog Food',
          price: 2999,
          description: 'High-quality nutrition for your dog',
          category: 'food',
          stock: 25,
          image: '',
          status: 'in_stock',
          createdAt: new Date().toISOString()
        },
        {
          _id: '2',
          name: 'Cat Toy Set',
          price: 1599,
          description: 'Interactive toys for cats',
          category: 'toys',
          stock: 5,
          image: '',
          status: 'low_stock',
          createdAt: new Date().toISOString()
        }
      ];
      setProducts(mockProducts);
      setError('Using demo data - API connection failed');
    } finally {
      setLoading(false);
    }
  };

  // Show notification
  const showNotification = (type, message) => {
    setNotification({ type, message });
    setTimeout(() => setNotification({ type: '', message: '' }), 3000);
  };

  // Get status color
  const getStatusColor = (product) => {
    if (product.isDeleted) return 'bg-red-100 text-red-800';
    if (product.stock === 0) return 'bg-gray-100 text-gray-800';
    if (product.stock < 10) return 'bg-yellow-100 text-yellow-800';
    return 'bg-green-100 text-green-800';
  };

  // Get status text
  const getStatusText = (product) => {
    if (product.isDeleted) return 'Deleted';
    if (product.stock === 0) return 'Out of Stock';
    if (product.stock < 10) return 'Low Stock';
    return 'In Stock';
  };

  // Update product
  const updateProduct = async (productId, updateData) => {
    try {
      const response = await productService.updateProduct(productId, updateData);

      if (response.success) {
        setProducts(products.map(product =>
          product._id === productId ? response.product : product
        ));
        showNotification('success', 'Product updated successfully!');
        setEditingProduct(null);
      } else {
        throw new Error('Failed to update product');
      }
    } catch (error) {
      console.error('Error updating product:', error);
      if (error.response?.data?.message) {
        showNotification('error', error.response.data.message);
      } else {
        showNotification('error', 'Failed to update product');
      }
    }
  };

  // Delete product
  const deleteProduct = async (productId) => {
    try {
      const response = await productService.deleteProduct(productId);

      if (response.success) {
        setProducts(products.filter(product => product._id !== productId));
        showNotification('success', 'Product deleted successfully!');
        setDeleteConfirm(null);
      } else {
        throw new Error('Failed to delete product');
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      if (error.response?.data?.message) {
        showNotification('error', error.response.data.message);
      } else {
        showNotification('error', 'Failed to delete product');
      }
      setDeleteConfirm(null);
    }
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch =
      product.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = categoryFilter === 'all' || product.category === categoryFilter;

    let matchesStatus = true;
    if (statusFilter !== 'all') {
      switch (statusFilter) {
        case 'in_stock':
          matchesStatus = product.stock >= 10 && !product.isDeleted;
          break;
        case 'low_stock':
          matchesStatus = product.stock > 0 && product.stock < 10 && !product.isDeleted;
          break;
        case 'out_of_stock':
          matchesStatus = product.stock === 0 && !product.isDeleted;
          break;
        case 'deleted':
          matchesStatus = product.isDeleted;
          break;
      }
    }

    return matchesSearch && matchesCategory && matchesStatus;
  });

  return (
    <div className="p-6">
      {/* Notification */}
      {notification.message && (
        <div className={`mb-4 p-4 rounded-lg ${
          notification.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
        }`}>
          {notification.message}
        </div>
      )}

      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <h2 className="text-2xl font-semibold text-gray-800">Product Management</h2>
          <p className="text-gray-600 mt-1">Manage your product catalog</p>
        </div>
        <div className="flex items-center space-x-3">
          <button className="bg-[#575CEE] text-white px-4 py-2 rounded-lg hover:bg-[#4a4fd1] transition-colors flex items-center">
            <FaPlus className="mr-2" />
            Add Product
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search by product name or description..."
            className="pl-10 pr-4 py-2 w-full border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex items-center space-x-2">
          <FaFilter className="text-gray-400" />
          <select
            className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
          >
            <option value="all">All Categories</option>
            {PRODUCT_CATEGORIES.map(category => (
              <option key={category.value} value={category.value}>
                {category.label}
              </option>
            ))}
          </select>
          <select
            className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            {PRODUCT_STATUS_OPTIONS.map(status => (
              <option key={status.value} value={status.value}>
                {status.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Products Table */}
      <div className="bg-white rounded-lg shadow overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              <tr>
                <td colSpan="7" className="px-6 py-4 text-center text-gray-500">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#575CEE]"></div>
                    <span className="ml-2">Loading products...</span>
                  </div>
                </td>
              </tr>
            ) : error ? (
              <tr>
                <td colSpan="7" className="px-6 py-4 text-center text-red-500">
                  {error}
                </td>
              </tr>
            ) : filteredProducts.length === 0 ? (
              <tr>
                <td colSpan="7" className="px-6 py-4 text-center text-gray-500">
                  {searchTerm || categoryFilter !== 'all' || statusFilter !== 'all'
                    ? 'No products found matching your criteria.'
                    : 'No products found.'}
                </td>
              </tr>
            ) : (
              filteredProducts.map((product) => (
                <tr key={product._id} className="hover:bg-gray-50">
                  {/* Product Info */}
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center">
                        {product.image ? (
                          <img src={product.image} alt={product.name} className="h-12 w-12 rounded-lg object-cover" />
                        ) : (
                          <FaBox className="text-gray-400" />
                        )}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {product.name}
                        </div>
                        <div className="text-sm text-gray-500 max-w-xs truncate">
                          {product.description}
                        </div>
                      </div>
                    </div>
                  </td>

                  {/* Category */}
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600 capitalize">
                    {product.category}
                  </td>

                  {/* Price */}
                  <td className="px-4 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">
                    ${typeof product.price === 'number' ? product.price.toFixed(2) : '0.00'}
                  </td>

                  {/* Stock */}
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600">
                    <div className="flex items-center">
                      {product.stock < 10 && product.stock > 0 && (
                        <FaExclamationTriangle className="text-yellow-500 mr-1" />
                      )}
                      {product.stock}
                    </div>
                  </td>

                  {/* Status */}
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs rounded-full font-medium ${getStatusColor(product)}`}>
                      {getStatusText(product)}
                    </span>
                  </td>

                  {/* Created Date */}
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(product.createdAt).toLocaleDateString()}
                  </td>

                  {/* Actions */}
                  <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setSelectedProduct(product)}
                        className="text-[#575CEE] hover:text-[#4a4fd1] flex items-center text-xs"
                      >
                        <FaEye className="mr-1" /> View
                      </button>
                      {!product.isDeleted && (
                        <>
                          <button
                            onClick={() => setEditingProduct(product)}
                            className="text-green-600 hover:text-green-800 flex items-center text-xs"
                          >
                            <FaEdit className="mr-1" /> Edit
                          </button>
                          <button
                            onClick={() => setDeleteConfirm(product)}
                            className="text-red-600 hover:text-red-800 flex items-center text-xs"
                          >
                            <FaTrash className="mr-1" /> Delete
                          </button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Product Details Modal */}
      {selectedProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Product Details</h3>
              <button
                onClick={() => setSelectedProduct(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Product Image */}
              <div>
                <h4 className="font-semibold text-gray-800 mb-3">Product Image</h4>
                <div className="w-full h-48 bg-gray-200 rounded-lg flex items-center justify-center">
                  {selectedProduct.image ? (
                    <img
                      src={selectedProduct.image}
                      alt={selectedProduct.name}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <FaBox className="text-gray-400 text-4xl" />
                  )}
                </div>
              </div>

              {/* Product Information */}
              <div>
                <h4 className="font-semibold text-gray-800 mb-3">Product Information</h4>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Name:</span> {selectedProduct.name}</p>
                  <p><span className="font-medium">Price:</span> ${typeof selectedProduct.price === 'number' ? selectedProduct.price.toFixed(2) : '0.00'}</p>
                  <p><span className="font-medium">Category:</span> <span className="capitalize">{selectedProduct.category}</span></p>
                  <p><span className="font-medium">Stock:</span> {selectedProduct.stock}</p>
                  <p><span className="font-medium">Status:</span>
                    <span className={`ml-2 px-2 py-1 text-xs rounded-full ${getStatusColor(selectedProduct)}`}>
                      {getStatusText(selectedProduct)}
                    </span>
                  </p>
                  <p><span className="font-medium">Created:</span> {new Date(selectedProduct.createdAt).toLocaleString()}</p>
                </div>
              </div>
            </div>

            {/* Product Description */}
            <div className="mt-6">
              <h4 className="font-semibold text-gray-800 mb-3">Description</h4>
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-gray-700">{selectedProduct.description}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Product Modal */}
      {editingProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Edit Product</h3>
              <button
                onClick={() => setEditingProduct(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <form onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.target);
              const updateData = {
                name: formData.get('name'),
                price: parseFloat(formData.get('price')), // Keep as dollars
                description: formData.get('description'),
                category: formData.get('category'),
                stock: parseInt(formData.get('stock'))
              };
              updateProduct(editingProduct._id, updateData);
            }}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Product Name</label>
                  <input
                    type="text"
                    name="name"
                    defaultValue={editingProduct.name}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#575CEE]"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Price ($)</label>
                  <input
                    type="number"
                    name="price"
                    step="0.01"
                    defaultValue={typeof editingProduct.price === 'number' ? editingProduct.price.toFixed(2) : '0.00'}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#575CEE]"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                  <select
                    name="category"
                    defaultValue={editingProduct.category}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#575CEE]"
                    required
                  >
                    {PRODUCT_CATEGORIES.map(category => (
                      <option key={category.value} value={category.value}>
                        {category.label}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Stock</label>
                  <input
                    type="number"
                    name="stock"
                    defaultValue={editingProduct.stock}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#575CEE]"
                    required
                  />
                </div>
              </div>
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  name="description"
                  rows="3"
                  defaultValue={editingProduct.description}
                  className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#575CEE]"
                  required
                />
              </div>
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setEditingProduct(null)}
                  className="px-4 py-2 text-gray-600 border rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-[#575CEE] text-white rounded-lg hover:bg-[#4a4fd1]"
                >
                  Update Product
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {deleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center mb-4">
              <FaExclamationTriangle className="text-red-500 text-2xl mr-3" />
              <h3 className="text-lg font-semibold">Confirm Deletion</h3>
            </div>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete "{deleteConfirm.name}"? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setDeleteConfirm(null)}
                className="px-4 py-2 text-gray-600 border rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => deleteProduct(deleteConfirm._id)}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                Delete Product
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductsSection;

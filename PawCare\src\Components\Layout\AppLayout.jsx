import React from 'react';
import HeaderNav from '../Header/Header.jsx';
import FooterComponent from '../Footer/Footer.jsx';

const AppLayout = ({ children }) => {
  // Use window.location instead of useLocation to avoid Router context issues
  let isAuthPage = false;

  try {
    if (typeof window !== 'undefined' && window.location) {
      const pathname = window.location.pathname;
      isAuthPage = pathname === '/login' || pathname === '/signup' || pathname === '/';
    }
  } catch (error) {
    console.warn('Could not access window.location:', error);
    // Default to showing header/footer if we can't determine the page
    isAuthPage = false;
  }

  return (
    <div className="w-full flex flex-col min-h-screen">
      {!isAuthPage && (
        <nav className='pt-5 pr-10 bg-[#575CEE] h-20'>
          <HeaderNav />
        </nav>
      )}

      <main className={`flex-grow ${isAuthPage ? 'flex items-center justify-center' : ''}`}>
        {children}
      </main>

      {!isAuthPage && (
        <footer className='mt-5 w-full'>
          <FooterComponent />
        </footer>
      )}
    </div>
  );
};

export default AppLayout;

// controller/paymentController.js
const stripe = require('../config/stripe');
const Order = require('../model/orderModel');

// Create Payment Intent
exports.createPaymentIntent = async (req, res) => {
  console.log('Received request body for payment intent:', req.body);
  try {
    const { amount, currency = 'usd', items } = req.body;

    // Validate amount
    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid amount'
      });
    }

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amount,
      currency: 'usd'
    });

    res.status(200).json({
      success: true,
      clientSecret: paymentIntent.client_secret
    });

  } catch (error) {
    console.error('Payment Error:', error);
    res.status(500).json({
      success: false,
      message: 'Payment failed',
      error: error.message
    });
  }
};

// Create new order after successful payment
exports.createOrder = async (req, res) => {
  try {
    const { products, paymentIntentId, amount, customerInfo, shippingAddress } = req.body;

    // Validate required fields
    if (!products?.length || !paymentIntentId || !amount) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields'
      });
    }

    // Verify payment intent with Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status !== 'succeeded') {
      return res.status(400).json({
        success: false,
        message: 'Payment not successful'
      });
    }

    // Create new order
    const order = await Order.create({
      products,
      paymentIntentId,
      amount,
      customerInfo,
      shippingAddress,
      status: 'completed',
      orderDate: new Date()
    });

    // Populate product details if needed
    await order.populate('products.product');

    res.status(201).json({
      success: true,
      order
    });

  } catch (error) {
    console.error('Create Order Error:', error);

    if (error.code === 11000) { // Duplicate payment intent
      return res.status(400).json({
        success: false,
        message: 'Order already exists for this payment'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create order',
      error: error.message
    });
  }
};

// Get all orders
exports.getAllOrders = async (req, res) => {
  try {
    const orders = await Order.find()
      .populate('products.product')
      .sort({ orderDate: -1 }); // Sort by newest first

    res.status(200).json({
      success: true,
      orders
    });
  } catch (error) {
    console.error('Get Orders Error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch orders',
      error: error.message
    });
  }
};
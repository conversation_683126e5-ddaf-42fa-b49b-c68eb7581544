import React, { createContext, useContext, useState, useEffect } from 'react';

// Create the AuthContext
const AuthContext = createContext();

// Custom hook to use the AuthContext
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// AuthProvider component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  // Check authentication status on mount and localStorage changes
  const checkAuthStatus = () => {
    try {
      const authFlag = localStorage.getItem('isAuthenticated');
      const token = localStorage.getItem('token');
      const userName = localStorage.getItem('userName');
      const userEmail = localStorage.getItem('userEmail');
      const userId = localStorage.getItem('userId');

      console.log('🔍 Checking auth status:', {
        authFlag: !!authFlag,
        token: !!token,
        userName,
        userEmail,
        userId
      });

      if (authFlag && token && (userName || userEmail)) {
        const userData = {
          id: userId,
          name: userName,
          email: userEmail
        };
        
        setUser(userData);
        setIsAuthenticated(true);
        console.log('✅ User authenticated:', userData);
      } else {
        setUser(null);
        setIsAuthenticated(false);
        console.log('❌ User not authenticated');
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setLoading(false);
    }
  };

  // Login function
  const login = (userData, token) => {
    try {
      console.log('🚀 AuthContext: Logging in user:', userData);
      
      // Store in localStorage
      localStorage.setItem('isAuthenticated', token);
      localStorage.setItem('token', token);
      localStorage.setItem('userName', userData.name);
      localStorage.setItem('userEmail', userData.email);
      localStorage.setItem('userId', userData._id || userData.id);

      // Update context state
      const user = {
        id: userData._id || userData.id,
        name: userData.name,
        email: userData.email
      };
      
      setUser(user);
      setIsAuthenticated(true);
      
      console.log('✅ AuthContext: User logged in successfully');
      
      // Dispatch custom event to notify other components
      window.dispatchEvent(new CustomEvent('authStateChanged', { 
        detail: { isAuthenticated: true, user } 
      }));
      
    } catch (error) {
      console.error('❌ AuthContext: Login error:', error);
    }
  };

  // Logout function
  const logout = () => {
    try {
      console.log('🚀 AuthContext: Logging out user');
      
      // Clear localStorage
      localStorage.removeItem('isAuthenticated');
      localStorage.removeItem('token');
      localStorage.removeItem('userName');
      localStorage.removeItem('userEmail');
      localStorage.removeItem('userId');
      localStorage.removeItem('lastOrderNumber');

      // Update context state
      setUser(null);
      setIsAuthenticated(false);
      
      console.log('✅ AuthContext: User logged out successfully');
      
      // Dispatch custom event to notify other components
      window.dispatchEvent(new CustomEvent('authStateChanged', { 
        detail: { isAuthenticated: false, user: null } 
      }));
      
    } catch (error) {
      console.error('❌ AuthContext: Logout error:', error);
    }
  };

  // Check auth status on mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  // Listen for storage changes (for multi-tab synchronization)
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'isAuthenticated' || e.key === 'token' || e.key === 'userName' || e.key === 'userEmail') {
        console.log('📡 Storage changed, rechecking auth status');
        checkAuthStatus();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // Listen for custom auth state change events
  useEffect(() => {
    const handleAuthStateChange = (e) => {
      console.log('📡 Auth state change event received:', e.detail);
      checkAuthStatus();
    };

    window.addEventListener('authStateChanged', handleAuthStateChange);
    return () => window.removeEventListener('authStateChanged', handleAuthStateChange);
  }, []);

  const value = {
    user,
    isAuthenticated,
    loading,
    login,
    logout,
    checkAuthStatus
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

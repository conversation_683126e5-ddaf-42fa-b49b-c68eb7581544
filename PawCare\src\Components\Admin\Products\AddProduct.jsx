import React, { useState } from 'react';
import { FaUpload, FaCheck, FaTimes } from 'react-icons/fa';
import { productService, PRODUCT_CATEGORIES } from '../../../services/productService';

const AddProduct = () => {
  const [productData, setProductData] = useState({
    name: '',
    description: '',
    price: '',
    category: '',
    stock: '',
    featured: false,
    image: []
  });

  const [imagePreview, setImagePreview] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState({ type: '', message: '' });

  // Handle input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setProductData({
      ...productData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle image upload
  const handleImageUpload = (e) => {
    const files = Array.from(e.target.files);

    // Create preview URLs for selected images
    const previews = files.map(file => URL.createObjectURL(file));
    setImagePreview([...imagePreview, ...previews]);

    // Store files for form submission
    setProductData({
      ...productData,
      image: [...productData.image, ...files]
    });
  };

  // Remove image from preview and form data
  const removeImage = (index) => {
    const updatedPreviews = imagePreview.filter((_, i) => i !== index);
    const updatedImages = productData.image.filter((_, i) => i !== index);

    setImagePreview(updatedPreviews);
    setProductData({
      ...productData,
      image: updatedImages
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitMessage({ type: '', message: '' });

    try {
      // Prepare product data for API
      const apiProductData = {
        name: productData.name.trim(),
        description: productData.description.trim(),
        price: parseFloat(productData.price), // Keep as dollars, backend will handle conversion if needed
        category: productData.category,
        stock: parseInt(productData.stock) || 0,
        featured: productData.featured
      };

      // Add first image if available
      if (productData.image.length > 0) {
        apiProductData.image = productData.image[0];
      }

      console.log('Creating new product:', apiProductData);

      // Submit to API using product service
      const result = await productService.createProduct(apiProductData);

      console.log('Success response:', result);

      // Reset form on success
      setProductData({
        name: '',
        description: '',
        price: '',
        category: '',
        stock: '',
        featured: false,
        image: []
      });
      setImagePreview([]);
      setSubmitMessage({
        type: 'success',
        message: 'Product added successfully!'
      });
    } catch (error) {
      console.error('Error adding product:', error);
      console.error('Error details:', error.response?.data || 'No detailed error information');
      console.error('Error status:', error.response?.status);

      let errorMessage = 'Failed to add product';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.errors) {
        errorMessage = error.response.data.errors.join(', ');
      } else if (error.message) {
        errorMessage = error.message;
      }

      setSubmitMessage({
        type: 'error',
        message: errorMessage
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Use categories from service to ensure consistency
  const categories = [
    ...PRODUCT_CATEGORIES,
    { value: 'bedding', label: 'Pet Bedding' },
    { value: 'other', label: 'Other' }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <h1 className="text-2xl font-semibold text-gray-800 mb-6">Add New Product</h1>

      {submitMessage.message && (
        <div className={`mb-4 p-4 rounded-lg ${
          submitMessage.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {submitMessage.type === 'success' ? (
            <div className="flex items-center">
              <FaCheck className="mr-2" /> {submitMessage.message}
            </div>
          ) : (
            <div className="flex items-center">
              <FaTimes className="mr-2" /> {submitMessage.message}
            </div>
          )}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Product Name */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Product Name*
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={productData.name}
              onChange={handleChange}
              required
              className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
            />
          </div>

          {/* Price */}
          <div>
            <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
              Price* ($)
            </label>
            <input
              type="number"
              id="price"
              name="price"
              value={productData.price}
              onChange={handleChange}
              required
              min="0"
              step="0.01"
              className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
            />
          </div>

          {/* Category */}
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
              Category*
            </label>
            <select
              id="category"
              name="category"
              value={productData.category}
              onChange={handleChange}
              required
              className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
            >
              <option value="">Select a category</option>
              {categories.map((category, index) => (
                <option key={index} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
          </div>

          {/* Stock */}
          <div>
            <label htmlFor="stock" className="block text-sm font-medium text-gray-700 mb-1">
              Stock*
            </label>
            <input
              type="number"
              id="stock"
              name="stock"
              value={productData.stock}
              onChange={handleChange}
              required
              min="0"
              className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
            />
          </div>
        </div>

        {/* Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
            Description*
          </label>
          <textarea
            id="description"
            name="description"
            value={productData.description}
            onChange={handleChange}
            required
            rows="4"
            className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
          />
        </div>

        {/* Featured Product */}
        <div className="flex items-center">
          <input
            type="checkbox"
            id="featured"
            name="featured"
            checked={productData.featured}
            onChange={handleChange}
            className="h-4 w-4 text-[#575CEE] focus:ring-[#575CEE] border-gray-300 rounded"
          />
          <label htmlFor="featured" className="ml-2 block text-sm text-gray-700">
            Featured Product
          </label>
        </div>

        {/* Image Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Product Images*
          </label>
          <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
            <div className="space-y-1 text-center">
              <FaUpload className="mx-auto h-12 w-12 text-gray-400" />
              <div className="flex text-sm text-gray-600">
                <label
                  htmlFor="file-upload"
                  className="relative cursor-pointer bg-white rounded-md font-medium text-[#575CEE] hover:text-[#4a4fd1] focus-within:outline-none"
                >
                  <span>Upload images</span>
                  <input
                    id="file-upload"
                    name="file-upload"
                    type="file"
                    multiple
                    accept="image/*"
                    className="sr-only"
                    onChange={handleImageUpload}
                  />
                </label>
                <p className="pl-1">or drag and drop</p>
              </div>
              <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
            </div>
          </div>
        </div>

        {/* Image Previews */}
        {imagePreview.length > 0 && (
          <div className="mt-4">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Image Previews</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {imagePreview.map((src, index) => (
                <div key={index} className="relative">
                  <img
                    src={src}
                    alt={`Preview ${index + 1}`}
                    className="h-24 w-full object-cover rounded-md"
                  />
                  <button
                    type="button"
                    className="absolute top-0 right-0 bg-red-500 text-white rounded-full p-1 transform translate-x-1/2 -translate-y-1/2"
                    onClick={() => removeImage(index)}
                  >
                    <FaTimes size={10} />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isSubmitting}
            className={`px-6 py-2 rounded-lg text-white ${
              isSubmitting ? 'bg-gray-400' : 'bg-[#575CEE] hover:bg-[#4a4fd1]'
            } transition-colors`}
          >
            {isSubmitting ? 'Adding Product...' : 'Add Product'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddProduct;

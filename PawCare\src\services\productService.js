import axios from 'axios';

const API_BASE_URL = 'http://localhost:3000/api/v0';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Product Service Functions
export const productService = {
  // Get all products with filters
  getAllProducts: async (params = {}) => {
    try {
      const response = await api.get('/products/all', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching products:', error);
      throw error;
    }
  },

  // Get single product by ID
  getProductById: async (productId) => {
    try {
      const response = await api.get(`/products/${productId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw error;
    }
  },

  // Create new product
  createProduct: async (productData) => {
    try {
      const formData = new FormData();

      // Append all product data to FormData
      Object.keys(productData).forEach(key => {
        if (productData[key] !== null && productData[key] !== undefined) {
          formData.append(key, productData[key]);
        }
      });

      const response = await api.post('/products/create', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  },

  // Update product
  updateProduct: async (productId, updateData) => {
    try {
      const formData = new FormData();

      // Append all update data to FormData
      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== null && updateData[key] !== undefined) {
          formData.append(key, updateData[key]);
        }
      });

      const response = await api.put(`/products/${productId}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  },

  // Delete product (soft delete)
  deleteProduct: async (productId) => {
    try {
      const response = await api.delete(`/products/${productId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  },

  // Get product statistics
  getProductStats: async () => {
    try {
      const response = await api.get('/products/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching product stats:', error);
      throw error;
    }
  },

  // Utility function to format product data for API
  formatProductForAPI: (productData) => {
    return {
      name: productData.name?.trim(),
      price: parseFloat(productData.price) * 100, // Convert to cents
      description: productData.description?.trim(),
      category: productData.category,
      stock: parseInt(productData.stock) || 0,
      image: productData.image || ''
    };
  },

  // Utility function to format product data from API
  formatProductFromAPI: (product) => {
    return {
      ...product,
      price: product.price / 100, // Convert from cents to dollars
      formattedPrice: `$${(product.price / 100).toFixed(2)}`,
      status: getProductStatus(product),
      statusColor: getProductStatusColor(product)
    };
  }
};

// Helper function to determine product status
const getProductStatus = (product) => {
  if (product.isDeleted) return 'deleted';
  if (product.stock === 0) return 'out_of_stock';
  if (product.stock < 10) return 'low_stock';
  return 'in_stock';
};

// Helper function to get status color
const getProductStatusColor = (product) => {
  const status = getProductStatus(product);
  switch (status) {
    case 'deleted':
      return 'bg-red-100 text-red-800';
    case 'out_of_stock':
      return 'bg-gray-100 text-gray-800';
    case 'low_stock':
      return 'bg-yellow-100 text-yellow-800';
    case 'in_stock':
      return 'bg-green-100 text-green-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// Product categories
export const PRODUCT_CATEGORIES = [
  { value: 'toys', label: 'Toys' },
  { value: 'food', label: 'Food' },
  { value: 'accessories', label: 'Accessories' },
  { value: 'health', label: 'Health' },
  { value: 'grooming', label: 'Grooming' },
  { value: 'bedding', label: 'Pet Bedding' },
  { value: 'other', label: 'Other' }
];

// Product status options
export const PRODUCT_STATUS_OPTIONS = [
  { value: 'all', label: 'All Status' },
  { value: 'in_stock', label: 'In Stock' },
  { value: 'low_stock', label: 'Low Stock' },
  { value: 'out_of_stock', label: 'Out of Stock' },
  { value: 'deleted', label: 'Deleted' }
];

export default productService;

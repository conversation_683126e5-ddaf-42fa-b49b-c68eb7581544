// Authentication utility functions
export const getAuthToken = () => {
  return localStorage.getItem('token');
};

export const getUserId = () => {
  return localStorage.getItem('userId');
};

export const isAuthenticated = () => {
  const token = getAuthToken();
  const authFlag = localStorage.getItem('isAuthenticated');
  return !!(token && authFlag);
};

export const getAuthHeaders = () => {
  const token = getAuthToken();
  if (!token) {
    console.warn('No authentication token found');
    return {};
  }
  
  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
};

export const clearAuthData = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('isAuthenticated');
  localStorage.removeItem('userId');
  localStorage.removeItem('userName');
  localStorage.removeItem('userEmail');
};

export const debugAuthState = () => {
  const authData = {
    token: getAuthToken(),
    userId: getUserId(),
    isAuthenticated: isAuthenticated(),
    userName: localStorage.getItem('userName'),
    userEmail: localStorage.getItem('userEmail'),
    authFlag: localStorage.getItem('isAuthenticated')
  };
  
  console.log('🔐 Auth Debug State:', authData);
  return authData;
};

import React, { createContext, useState, useContext, useEffect } from 'react';

// Create the cart context
const CartContext = createContext();

// Custom hook to use the cart context
export const useCart = () => {
  return useContext(CartContext);
};

// Cart provider component
export const CartProvider = ({ children }) => {
  const [cartItems, setCartItems] = useState([]);
  const [cartCount, setCartCount] = useState(0);
  const [cartTotal, setCartTotal] = useState(0);

  // Load cart from localStorage on initial render
  useEffect(() => {
    console.log('🔍 CartContext: Loading cart from localStorage...');

    const storedCart = localStorage.getItem('pawcare_cart');
    if (storedCart) {
      try {
        const parsedCart = JSON.parse(storedCart);
        console.log('✅ CartContext: Loaded cart items:', parsedCart);

        // Validate cart items structure
        const validItems = parsedCart.filter(item => {
          const isValid = item.id && item.title && item.price && item.quantity;
          if (!isValid) {
            console.warn('⚠️ CartContext: Invalid cart item found:', item);
          }
          return isValid;
        });

        if (validItems.length !== parsedCart.length) {
          console.log(`🔧 CartContext: Filtered ${parsedCart.length - validItems.length} invalid items`);
        }

        setCartItems(validItems);
      } catch (error) {
        console.error('❌ CartContext: Error parsing cart data:', error);
        localStorage.removeItem('pawcare_cart');
        setCartItems([]);
      }
    } else {
      console.log('ℹ️ CartContext: No stored cart found, starting with empty cart');
      setCartItems([]);
    }
  }, []);

  // Update localStorage and cart count/total whenever cartItems changes
  useEffect(() => {
    localStorage.setItem('pawcare_cart', JSON.stringify(cartItems));

    // Calculate cart count and total
    const count = cartItems.reduce((total, item) => total + item.quantity, 0);
    setCartCount(count);

    const total = cartItems.reduce((total, item) => {
      // Remove $ sign and convert to number
      const priceNum = parseFloat(item.price.replace('$', ''));
      return total + (priceNum * item.quantity);
    }, 0);
    setCartTotal(total);
  }, [cartItems]);

  // Add item to cart
  const addToCart = (product) => {
    console.log('🛒 CartContext: Adding product to cart:', product);

    // Validate product structure
    if (!product.id) {
      console.error('❌ CartContext: Product missing ID:', product);
      return;
    }
    if (!product.title) {
      console.error('❌ CartContext: Product missing title:', product);
      return;
    }
    if (!product.price) {
      console.error('❌ CartContext: Product missing price:', product);
      return;
    }

    setCartItems(prevItems => {
      console.log('🔍 CartContext: Current cart items:', prevItems);

      // Check if item already exists in cart
      const existingItemIndex = prevItems.findIndex(item => item.id === product.id);

      if (existingItemIndex !== -1) {
        // Item exists, update quantity
        console.log(`✅ CartContext: Updating quantity for existing item: ${product.title}`);
        const updatedItems = [...prevItems];
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: updatedItems[existingItemIndex].quantity + 1
        };
        return updatedItems;
      } else {
        // Item doesn't exist, add new item
        console.log(`✅ CartContext: Adding new item to cart: ${product.title}`);
        const newItem = { ...product, quantity: 1 };
        console.log('🔍 CartContext: New item structure:', newItem);
        return [...prevItems, newItem];
      }
    });
  };

  // Remove item from cart
  const removeFromCart = (productId) => {
    setCartItems(prevItems => prevItems.filter(item => item.id !== productId));
  };

  // Update item quantity
  const updateQuantity = (productId, quantity) => {
    if (quantity <= 0) {
      removeFromCart(productId);
      return;
    }

    setCartItems(prevItems =>
      prevItems.map(item =>
        item.id === productId ? { ...item, quantity } : item
      )
    );
  };

  // Clear cart
  const clearCart = () => {
    setCartItems([]);
  };

  // Context value
  const value = {
    cartItems,
    cartCount,
    cartTotal,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};

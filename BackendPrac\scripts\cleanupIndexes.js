/**
 * Database Index Cleanup Script
 * 
 * This script cleans up duplicate indexes in the MongoDB database
 * Run this once after updating the Order model schema
 */

const mongoose = require('mongoose');
const Order = require('../model/orderModel');

// Database connection configuration
const connectDB = async () => {
    try {
        // Use the same connection string as your main application
        const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/pawcare';
        
        await mongoose.connect(mongoURI, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });
        
        console.log('✅ Connected to MongoDB for index cleanup');
    } catch (error) {
        console.error('❌ MongoDB connection error:', error);
        process.exit(1);
    }
};

// Main cleanup function
const cleanupIndexes = async () => {
    try {
        console.log('🚀 Starting index cleanup process...');
        
        // Connect to database
        await connectDB();
        
        // Clean up Order model indexes
        console.log('\n📋 Cleaning up Order model indexes...');
        const result = await Order.cleanupIndexes();
        
        if (result.success) {
            console.log('✅ Order indexes cleaned up successfully');
        } else {
            console.error('❌ Failed to clean up Order indexes:', result.error);
        }
        
        // You can add other models here if needed
        // Example:
        // const Product = require('../model/productModel');
        // await Product.cleanupIndexes();
        
        console.log('\n🎉 Index cleanup completed!');
        
    } catch (error) {
        console.error('❌ Error during index cleanup:', error);
    } finally {
        // Close database connection
        await mongoose.connection.close();
        console.log('📝 Database connection closed');
        process.exit(0);
    }
};

// Run the cleanup if this script is executed directly
if (require.main === module) {
    cleanupIndexes();
}

module.exports = { cleanupIndexes };

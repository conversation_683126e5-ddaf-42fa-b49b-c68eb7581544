const mongoose = require('mongoose');

const OrderSchema = new mongoose.Schema({
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'User is required']
    },
    orderNumber: {
        type: String,
        unique: true,
        required: true
    },
    products: [{
        product: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Product',
            required: [true, 'Product is required']
        },
        name: {
            type: String,
            required: true
        },
        price: {
            type: Number,
            required: true
        },
        quantity: {
            type: Number,
            required: [true, 'Quantity is required'],
            min: [1, 'Quantity must be at least 1']
        }
    }],
    amount: {
        type: Number,
        required: [true, 'Amount is required'],
        min: [0, 'Amount cannot be negative']
    },
    paymentIntentId: {
        type: String,
        required: [true, 'Payment Intent ID is required'],
        unique: true
    },
    paymentStatus: {
        type: String,
        enum: ['pending', 'succeeded', 'failed', 'canceled'],
        default: 'pending'
    },
    status: {
        type: String,
        enum: {
            values: ['pending', 'processing', 'shipped', 'delivered', 'completed', 'canceled', 'failed'],
            message: '{VALUE} is not a valid status'
        },
        default: 'pending'
    },
    customerInfo: {
        name: {
            type: String,
            required: true
        },
        email: {
            type: String,
            required: true
        },
        phone: {
            type: String,
            required: true
        }
    },
    shippingAddress: {
        address: {
            type: String,
            required: true
        },
        city: {
            type: String,
            required: true
        },
        postalCode: {
            type: String,
            required: true
        },
        country: {
            type: String,
            required: true,
            default: 'US'
        }
    },
    orderDate: {
        type: Date,
        default: Date.now
    },
    estimatedDelivery: {
        type: Date
    },
    trackingNumber: {
        type: String
    },
    notes: {
        type: String
    },
    isDeleted: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: true
});

// Generate order number before saving
OrderSchema.pre('save', async function(next) {
    if (this.isNew && !this.orderNumber) {
        const count = await this.constructor.countDocuments();
        this.orderNumber = `PW${Date.now().toString().slice(-6)}${(count + 1).toString().padStart(4, '0')}`;
    }
    next();
});

// Define indexes for efficient querying
// Note: paymentIntentId and orderNumber indexes are automatically created by unique: true
OrderSchema.index({ user: 1, createdAt: -1 });
OrderSchema.index({ status: 1, createdAt: -1 });
OrderSchema.index({ 'customerInfo.email': 1 });
OrderSchema.index({ isDeleted: 1 });

// Virtual for order total with currency formatting
OrderSchema.virtual('formattedAmount').get(function() {
    return `$${(this.amount / 100).toFixed(2)}`;
});

// Virtual for order age
OrderSchema.virtual('orderAge').get(function() {
    const now = new Date();
    const orderDate = this.createdAt || this.orderDate;
    const diffTime = Math.abs(now - orderDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
});

// Ensure virtual fields are serialized
OrderSchema.set('toJSON', { virtuals: true });
OrderSchema.set('toObject', { virtuals: true });

// Static method to clean up duplicate indexes (run once after schema update)
OrderSchema.statics.cleanupIndexes = async function() {
    try {
        console.log('🔧 Cleaning up duplicate indexes...');

        // Get current indexes
        const indexes = await this.collection.getIndexes();
        console.log('📋 Current indexes:', Object.keys(indexes));

        // Drop and recreate indexes to ensure clean state
        await this.collection.dropIndexes();
        console.log('✅ Dropped all indexes');

        // Recreate indexes (Mongoose will handle this automatically)
        await this.ensureIndexes();
        console.log('✅ Recreated indexes');

        // Show final indexes
        const finalIndexes = await this.collection.getIndexes();
        console.log('📋 Final indexes:', Object.keys(finalIndexes));

        return { success: true, message: 'Indexes cleaned up successfully' };
    } catch (error) {
        console.error('❌ Error cleaning up indexes:', error);
        return { success: false, error: error.message };
    }
};

const Order = mongoose.model('Order', OrderSchema);

module.exports = Order;
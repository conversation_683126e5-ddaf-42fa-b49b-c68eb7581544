import React, { useState, useEffect } from 'react';
import { <PERSON>aS<PERSON>ch, <PERSON>a<PERSON>ye, FaEdit, FaTrash, FaFilter, FaDownload } from 'react-icons/fa';
import axios from 'axios';

const OrdersSection = () => {
  const [orders, setOrders] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [notification, setNotification] = useState({ type: '', message: '' });

  useEffect(() => {
    fetchOrders();
  }, []);

  // Refetch orders when filters change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchOrders();
    }, 500); // Debounce search

    return () => clearTimeout(timeoutId);
  }, [searchTerm, statusFilter]);

  const fetchOrders = async () => {
    setLoading(true);
    try {
      // Check authentication
      const token = localStorage.getItem('token');
      const isAuthenticated = localStorage.getItem('isAuthenticated');

      console.log('Auth check:', {
        hasToken: !!token,
        isAuthenticated,
        tokenLength: token?.length
      });

      const response = await axios.get('http://localhost:3000/api/v0/orders/all', {
        params: {
          page: 1,
          limit: 50,
          status: statusFilter !== 'all' ? statusFilter : undefined,
          search: searchTerm || undefined
        },
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        setOrders(response.data.orders);
        setError(null);
      } else {
        throw new Error('Failed to fetch orders');
      }
    } catch (err) {
      console.error('Error fetching orders:', err);
      console.error('Error response:', err.response?.data);
      console.error('Error status:', err.response?.status);

      // Check if it's an authentication error
      if (err.response?.status === 401 || err.response?.status === 403) {
        setError('Authentication failed - Please log in again');
      } else if (err.response?.status === 404) {
        setError('Orders API endpoint not found - Using demo data');
      } else {
        setError('Using demo data - API connection failed');
      }

      // Fallback to mock data if API fails
      const mockOrders = [
        {
          _id: '1',
          orderNumber: 'PW123456',
          paymentIntentId: 'pi_1234567890',
          customerInfo: {
            name: 'John Doe',
            email: '<EMAIL>',
            phone: '+1234567890'
          },
          amount: 2500, // in cents
          status: 'completed',
          createdAt: new Date().toISOString(),
          products: [
            { product: { name: 'Dog Food' }, quantity: 2 },
            { product: { name: 'Cat Toy' }, quantity: 1 }
          ]
        },
        {
          _id: '2',
          orderNumber: 'PW123457',
          paymentIntentId: 'pi_0987654321',
          customerInfo: {
            name: 'Jane Smith',
            email: '<EMAIL>',
            phone: '+0987654321'
          },
          amount: 1800,
          status: 'pending',
          createdAt: new Date(Date.now() - 86400000).toISOString(),
          products: [
            { product: { name: 'Pet Bed' }, quantity: 1 }
          ]
        }
      ];
      setOrders(mockOrders);
    } finally {
      setLoading(false);
    }
  };

  // Show notification
  const showNotification = (type, message) => {
    setNotification({ type, message });
    setTimeout(() => setNotification({ type: '', message: '' }), 3000);
  };

  // Update order status
  const updateOrderStatus = async (orderId, newStatus) => {
    try {
      const response = await axios.put(`http://localhost:3000/api/v0/orders/${orderId}/status`, {
        status: newStatus
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.data.success) {
        setOrders(orders.map(order =>
          order._id === orderId ? { ...order, status: newStatus } : order
        ));
        showNotification('success', 'Order status updated successfully!');
      } else {
        throw new Error('Failed to update order status');
      }
    } catch (error) {
      console.error('Error updating order status:', error);
      // Fallback to local update for demo
      setOrders(orders.map(order =>
        order._id === orderId ? { ...order, status: newStatus } : order
      ));
      showNotification('error', 'Status updated locally (API connection failed)');
    }
  };

  const filteredOrders = orders.filter(order => {
    const matchesSearch =
      order.customerInfo?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customerInfo?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.paymentIntentId?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  return (
    <div className="p-6">
      {/* Notification */}
      {notification.message && (
        <div className={`mb-4 p-4 rounded-lg ${
          notification.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
        }`}>
          {notification.message}
        </div>
      )}

      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <h2 className="text-2xl font-semibold text-gray-800">Order Management</h2>
          <p className="text-gray-600 mt-1">Manage and track all customer orders</p>
        </div>
        <div className="flex items-center space-x-3">
          <button className="bg-[#575CEE] text-white px-4 py-2 rounded-lg hover:bg-[#4a4fd1] transition-colors flex items-center">
            <FaDownload className="mr-2" />
            Export Orders
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search by customer name, email, or order ID..."
            className="pl-10 pr-4 py-2 w-full border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex items-center space-x-2">
          <FaFilter className="text-gray-400" />
          <select
            className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#575CEE] focus:border-transparent"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="completed">Completed</option>
            <option value="failed">Failed</option>
          </select>
        </div>
      </div>

      {/* Orders Table */}
      <div className="bg-white rounded-lg shadow overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order Number</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shipping Address</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Products</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              <tr>
                <td colSpan="10" className="px-6 py-4 text-center text-gray-500">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#575CEE]"></div>
                    <span className="ml-2">Loading orders...</span>
                  </div>
                </td>
              </tr>
            ) : error ? (
              <tr>
                <td colSpan="10" className="px-6 py-4 text-center text-red-500">
                  {error}
                </td>
              </tr>
            ) : filteredOrders.length === 0 ? (
              <tr>
                <td colSpan="10" className="px-6 py-4 text-center text-gray-500">
                  {searchTerm || statusFilter !== 'all' ? 'No orders found matching your criteria.' : 'No orders found.'}
                </td>
              </tr>
            ) : (
              filteredOrders.map((order) => (
                <tr key={order._id} className="hover:bg-gray-50">
                  {/* Order Number */}
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                    <div className="font-semibold">{order.orderNumber || 'N/A'}</div>
                    <div className="text-xs text-gray-500">
                      {order.paymentIntentId?.substring(0, 15)}...
                    </div>
                  </td>

                  {/* Customer Name */}
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8 rounded-full bg-[#575CEE] flex items-center justify-center text-white font-semibold text-sm">
                        {(order.customerInfo?.name || '').charAt(0).toUpperCase()}
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">
                          {order.customerInfo?.name || 'N/A'}
                        </div>
                      </div>
                    </div>
                  </td>

                  {/* Customer Email */}
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600">
                    <div className="max-w-xs truncate" title={order.customerInfo?.email || 'N/A'}>
                      {order.customerInfo?.email || 'N/A'}
                    </div>
                  </td>

                  {/* Customer Phone */}
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600">
                    {order.customerInfo?.phone || 'N/A'}
                  </td>
                  {/* Shipping Address */}
                  <td className="px-4 py-4 text-sm text-gray-500">
                    <div className="max-w-xs">
                      {order.shippingAddress ? (
                        <div className="text-xs leading-relaxed">
                          <div className="font-medium">{order.shippingAddress.address}</div>
                          <div>{order.shippingAddress.city}, {order.shippingAddress.postalCode}</div>
                          <div>{order.shippingAddress.country}</div>
                        </div>
                      ) : (
                        <span className="text-gray-400">No address</span>
                      )}
                    </div>
                  </td>

                  {/* Products */}
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="max-w-xs">
                      {order.products?.slice(0, 2).map((item, index) => (
                        <div key={index} className="text-xs">
                          <span className="font-medium">{item.name || item.product?.name || 'Unknown Product'}</span> (x{item.quantity})
                        </div>
                      ))}
                      {order.products?.length > 2 && (
                        <div className="text-xs text-gray-400">
                          +{order.products.length - 2} more items
                        </div>
                      )}
                    </div>
                  </td>

                  {/* Amount */}
                  <td className="px-4 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">
                    ${(order.amount / 100).toFixed(2)}
                  </td>
                  {/* Status */}
                  <td className="px-4 py-4 whitespace-nowrap">
                    <select
                      value={order.status}
                      onChange={(e) => updateOrderStatus(order._id, e.target.value)}
                      className={`px-2 py-1 text-xs rounded-full border-0 font-semibold focus:outline-none focus:ring-2 focus:ring-[#575CEE] ${
                        order.status === 'completed' ? 'bg-green-100 text-green-800' :
                        order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        order.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                        order.status === 'shipped' ? 'bg-purple-100 text-purple-800' :
                        order.status === 'failed' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}
                    >
                      <option value="pending">Pending</option>
                      <option value="processing">Processing</option>
                      <option value="shipped">Shipped</option>
                      <option value="completed">Completed</option>
                      <option value="failed">Failed</option>
                    </select>
                  </td>

                  {/* Date */}
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="text-xs">
                      {new Date(order.createdAt).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-gray-400">
                      {new Date(order.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </div>
                  </td>

                  {/* Actions */}
                  <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => setSelectedOrder(order)}
                      className="text-[#575CEE] hover:text-[#4a4fd1] flex items-center text-xs"
                    >
                      <FaEye className="mr-1" /> View
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Order Details Modal */}
      {selectedOrder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Order Details</h3>
              <button
                onClick={() => setSelectedOrder(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Customer Information */}
              <div>
                <h4 className="font-semibold text-gray-800 mb-3">Customer Information</h4>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Name:</span> {selectedOrder.customerInfo?.name || 'N/A'}</p>
                  <p><span className="font-medium">Email:</span> {selectedOrder.customerInfo?.email || 'N/A'}</p>
                  <p><span className="font-medium">Phone:</span> {selectedOrder.customerInfo?.phone || 'N/A'}</p>
                </div>
              </div>

              {/* Order Information */}
              <div>
                <h4 className="font-semibold text-gray-800 mb-3">Order Information</h4>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Order Number:</span> {selectedOrder.orderNumber || 'N/A'}</p>
                  <p><span className="font-medium">Payment ID:</span>
                    <span className="font-mono text-xs ml-1">{selectedOrder.paymentIntentId}</span>
                  </p>
                  <p><span className="font-medium">Status:</span>
                    <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                      selectedOrder.status === 'completed' ? 'bg-green-100 text-green-800' :
                      selectedOrder.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      selectedOrder.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                      selectedOrder.status === 'shipped' ? 'bg-purple-100 text-purple-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {selectedOrder.status}
                    </span>
                  </p>
                  <p><span className="font-medium">Date:</span> {new Date(selectedOrder.createdAt).toLocaleString()}</p>
                  <p><span className="font-medium">Total:</span> ${(selectedOrder.amount / 100).toFixed(2)}</p>
                </div>
              </div>
            </div>

            {/* Shipping Address */}
            <div className="mt-6">
              <h4 className="font-semibold text-gray-800 mb-3">Shipping Address</h4>
              <div className="bg-gray-50 rounded-lg p-4">
                {selectedOrder.shippingAddress ? (
                  <div className="text-sm space-y-1">
                    <p className="font-medium">{selectedOrder.shippingAddress.address}</p>
                    <p>{selectedOrder.shippingAddress.city}, {selectedOrder.shippingAddress.postalCode}</p>
                    <p>{selectedOrder.shippingAddress.country}</p>
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">No shipping address provided</p>
                )}
              </div>
            </div>

            {/* Products */}
            <div className="mt-6">
              <h4 className="font-semibold text-gray-800 mb-3">Order Items</h4>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="space-y-3">
                  {selectedOrder.products?.map((item, index) => (
                    <div key={index} className="flex justify-between items-center py-3 px-3 bg-white rounded border">
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">
                          {item.name || item.product?.name || 'Unknown Product'}
                        </p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                          <span>Quantity: {item.quantity}</span>
                          <span>Unit Price: ${((item.price || item.product?.price || 0) / 100).toFixed(2)}</span>
                        </div>
                        {item.originalProductId && (
                          <p className="text-xs text-gray-400 mt-1">
                            Product ID: {item.originalProductId}
                          </p>
                        )}
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-gray-900">
                          ${(((item.price || item.product?.price || 0) * item.quantity) / 100).toFixed(2)}
                        </p>
                      </div>
                    </div>
                  ))}

                  {/* Order Total */}
                  <div className="border-t pt-3 mt-3">
                    <div className="flex justify-between items-center">
                      <span className="font-semibold text-gray-900">Total Amount:</span>
                      <span className="font-bold text-lg text-[#575CEE]">
                        ${(selectedOrder.amount / 100).toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrdersSection;

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multer Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>🧪 Multer Upload Test</h1>
    <p>This page tests the multer file upload functionality directly.</p>

    <form id="testForm">
        <div class="form-group">
            <label for="name">Product Name:</label>
            <input type="text" id="name" name="name" value="Test Product" required>
        </div>

        <div class="form-group">
            <label for="price">Price:</label>
            <input type="number" id="price" name="price" value="29.99" step="0.01" required>
        </div>

        <div class="form-group">
            <label for="category">Category:</label>
            <select id="category" name="category" required>
                <option value="toys">Toys</option>
                <option value="food">Food</option>
                <option value="accessories">Accessories</option>
            </select>
        </div>

        <div class="form-group">
            <label for="description">Description:</label>
            <textarea id="description" name="description" required>Test product description</textarea>
        </div>

        <div class="form-group">
            <label for="stock">Stock:</label>
            <input type="number" id="stock" name="stock" value="10" required>
        </div>

        <div class="form-group">
            <label for="image">Image:</label>
            <input type="file" id="image" name="image" accept="image/*" required>
        </div>

        <button type="button" onclick="testMulter()">🧪 Test Multer Only</button>
        <button type="button" onclick="testFullProduct()">📦 Test Full Product Creation</button>
    </form>

    <div id="result"></div>

    <script>
        async function testMulter() {
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            const resultDiv = document.getElementById('result');

            try {
                console.log('🧪 Testing multer upload...');
                
                // Log FormData contents
                console.log('📦 FormData contents:');
                for (let [key, value] of formData.entries()) {
                    if (value instanceof File) {
                        console.log(`  ${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);
                    } else {
                        console.log(`  ${key}: ${value}`);
                    }
                }

                const response = await fetch('http://localhost:3000/api/v0/product/test-upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                console.log('✅ Multer test result:', result);

                resultDiv.className = 'result ' + (result.success ? 'success' : 'error');
                resultDiv.textContent = JSON.stringify(result, null, 2);

            } catch (error) {
                console.error('❌ Multer test error:', error);
                resultDiv.className = 'result error';
                resultDiv.textContent = 'Error: ' + error.message;
            }
        }

        async function testFullProduct() {
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            const resultDiv = document.getElementById('result');

            try {
                console.log('📦 Testing full product creation...');
                
                // Log FormData contents
                console.log('📦 FormData contents:');
                for (let [key, value] of formData.entries()) {
                    if (value instanceof File) {
                        console.log(`  ${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);
                    } else {
                        console.log(`  ${key}: ${value}`);
                    }
                }

                const response = await fetch('http://localhost:3000/api/v0/product/create', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                console.log('✅ Product creation result:', result);

                resultDiv.className = 'result ' + (result.success ? 'success' : 'error');
                resultDiv.textContent = JSON.stringify(result, null, 2);

            } catch (error) {
                console.error('❌ Product creation error:', error);
                resultDiv.className = 'result error';
                resultDiv.textContent = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>

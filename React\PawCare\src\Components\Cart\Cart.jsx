/**
 * Cart Component with Stripe Payment Integration
 *
 * This component provides cart functionality with integrated Stripe payment processing.
 *
 * Current Implementation:
 * Real backend integration with http://localhost:3000/api/v0/payment/create-payment-intent
 * Proper amount calculation in cents format
 * Error handling and loading states
 * Payment confirmation with backend
 * Full Stripe.js integration with Elements
 *
 * Stripe Integration Structure:
 * - Backend API endpoints configured at /api/v0/payment/
 * - create-payment-intent: Creates payment intent with amount in cents
 * - confirm-payment: Confirms payment after successful processing
 * - create-customer: Creates customer record if needed
 *
 * Key Features:
 * - Secure card input with Stripe Elements
 * - Real-time card validation
 * - Payment processing with stripe.confirmCardPayment()
 * - Complete error handling for payment failures
 *
 * Requirements:
 * - Minimum payment amount: $0.50 (50 cents)
 * - Amount must be sent to backend in cents format
 * - Proper error handling for payment failures
 * - Loading states during payment processing
 */

import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { FaTrash, FaArrowLeft, FaPlus, FaMinus, FaCreditCard, FaSpinner, FaUser, FaEnvelope, FaPhone, FaMapMarkerAlt } from 'react-icons/fa';
import { useCart } from '../../context/CartContext';
import CardInput from './CardInput';
import axios from 'axios'; // Import axios
import { Payment_Url } from '../Url/BaseUrl';

const Cart = () => {
  const { cartItems, cartTotal, removeFromCart, updateQuantity, clearCart } = useCart();

  // State for checkout process
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentError, setPaymentError] = useState('');
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [clientSecret, setClientSecret] = useState('');
  const [showCardInput, setShowCardInput] = useState(false);
  const [showUserInfoForm, setShowUserInfoForm] = useState(false);

  // User information state
  const [userInfo, setUserInfo] = useState({
    name: localStorage.getItem('userName') || '',
    email: localStorage.getItem('userEmail') || '',
    phone: '',
    address: ''
  });

  // User info form errors
  const [userInfoErrors, setUserInfoErrors] = useState({});

  // Calculate amount in cents for Stripe (backend requirement)
  const calculateAmountInCents = () => {
    return Math.round(cartTotal * 100);
  };

  // Validate minimum amount ($0.50 as per backend requirement)
  const isValidAmount = () => {
    return cartTotal >= 0.50;
  };

  // Handle user info form input changes
  const handleUserInfoChange = (e) => {
    const { name, value } = e.target;
    setUserInfo(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error for this field if any
    if (userInfoErrors[name]) {
      setUserInfoErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Validate user information
  const validateUserInfo = () => {
    const errors = {};

    if (!userInfo.name.trim()) errors.name = 'Name is required';
    if (!userInfo.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(userInfo.email)) {
      errors.email = 'Email is invalid';
    }
    if (!userInfo.phone.trim()) errors.phone = 'Phone number is required';
    if (!userInfo.address.trim()) errors.address = 'Address is required';

    setUserInfoErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle proceed to payment - collects user info first
  const handleProceedToPayment = () => {
    setPaymentError('');
    setShowUserInfoForm(true);
  };

  // Handle the initial checkout process - after user info validation creates payment intent and shows card form
  const handleCheckout = async () => {
    // Reset previous states
    setPaymentError('');
    setPaymentSuccess(false);

    // Validate user information
    if (!validateUserInfo()) {
      return;
    }

    // Validate cart and amount
    if (cartItems.length === 0) {
      setPaymentError('Your cart is empty');
      return;
    }

    if (!isValidAmount()) {
      setPaymentError('Minimum order amount is $0.50');
      return;
    }

    setIsProcessingPayment(true);

    try {
      // Step 1: Create payment intent with backend using imported URLs
      console.log('Creating payment intent for amount:', calculateAmountInCents(), 'cents');

      const paymentIntentUrl = `${Payment_Url}/create-payment-intent`;
      console.log('Debug: Payment intent URL', paymentIntentUrl);

      const paymentIntentResponse = await fetch(paymentIntentUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ amount: calculateAmountInCents(), currency: 'usd' }),
      });

      if (!paymentIntentResponse.ok) {
        const errorData = await paymentIntentResponse.json().catch(() => ({}));
        console.error('Payment intent response error:', { status: paymentIntentResponse.status, errorData });
        throw new Error(errorData.message || 'Failed to create payment intent');
      }

      const { clientSecret } = await paymentIntentResponse.json();
      console.log('Payment intent created successfully, clientSecret received');

      // Set client secret and show card input
      setClientSecret(clientSecret);
      setShowCardInput(true);
      setIsProcessingPayment(false);

    } catch (error) {
      console.error('Payment error:', error);
      setPaymentError(error.message || 'Failed to initialize payment. Please try again.');
      setIsProcessingPayment(false);
      setShowCardInput(false);
    }
  };

  // Handle successful payment
  const handlePaymentSuccess = async () => {
    setPaymentSuccess(true);
    clearCart();  // Clear cart after successful payment to maintain expected behavior
    setPaymentError('');
  };

  // Handle payment error
  const handlePaymentError = (errorMessage) => {
    setPaymentError(errorMessage || 'Payment failed. Please try again.');
    setIsProcessingPayment(false);
  };

  // Return empty cart or success message if no items
  if (cartItems.length === 0 || paymentSuccess) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          {paymentSuccess ? (
            <div className="py-8">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-10 h-10 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">Payment Successful!</h2>
              <p className="text-gray-600 mb-6">Thank you for your purchase.</p>
              <Link to="/home" className="inline-flex items-center text-[#575CEE] hover:text-[#4a4fd1]">
                <FaArrowLeft className="mr-2" />
                Continue Shopping
              </Link>
            </div>
          ) : (
            <div className="py-8">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">Your cart is empty</h2>
              <p className="text-gray-600 mb-6">Looks like you haven't added any items to your cart yet.</p>
              <Link to="/home" className="inline-flex items-center text-[#575CEE] hover:text-[#4a4fd1]">
                <FaArrowLeft className="mr-2" />
                Start Shopping
              </Link>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Your Cart</h1>
          <button
            onClick={clearCart}
            className="text-red-500 hover:text-red-700 flex items-center"
          >
            <FaTrash className="mr-1" /> Clear Cart
          </button>
        </div>

        {/* Side-by-side layout for checkout process */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Left side - Cart items */}
          <div className="lg:w-1/2">
            <h2 className="text-lg font-medium mb-4">Items in Your Cart</h2>
            <div className="border-t border-gray-200 pt-4">
              {cartItems.map((item) => (
                <div key={item.id} className="flex flex-col sm:flex-row items-center py-4 border-b border-gray-200">
                  <div className="w-20 h-20 flex-shrink-0 bg-gray-100 rounded-md overflow-hidden mr-4 mb-4 sm:mb-0">
                    <img
                      src={item.img}
                      alt={item.title}
                      className="w-full h-full object-contain p-2"
                    />
                  </div>

                  <div className="flex-grow">
                    <h3 className="font-medium text-gray-900">{item.title}</h3>
                    <p className="text-[#575CEE] font-bold mt-1">{item.price}</p>
                  </div>

                  <div className="flex items-center mt-4 sm:mt-0">
                    <button
                      onClick={() => updateQuantity(item.id, item.quantity - 1)}
                      className="p-1 rounded-full bg-gray-200 hover:bg-gray-300"
                    >
                      <FaMinus className="text-gray-600" />
                    </button>

                    <span className="mx-3 w-6 text-center">{item.quantity}</span>

                    <button
                      onClick={() => updateQuantity(item.id, item.quantity + 1)}
                      className="p-1 rounded-full bg-gray-200 hover:bg-gray-300"
                    >
                      <FaPlus className="text-gray-600" />
                    </button>

                    <button
                      onClick={() => removeFromCart(item.id)}
                      className="ml-3 text-red-500 hover:text-red-700"
                    >
                      <FaTrash />
                    </button>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-4">
              <div className="flex justify-between text-lg font-medium">
                <span>Subtotal</span>
                <span>${cartTotal.toFixed(2)}</span>
              </div>

              {/* Amount validation message */}
              {cartTotal > 0 && cartTotal < 0.50 && (
                <p className="text-amber-600 text-sm mt-1">
                  Minimum order amount is $0.50
                </p>
              )}
            </div>
          </div>

          {/* Right side - User info form or payment */}
          <div className="lg:w-1/2 border-t lg:border-t-0 lg:border-l border-gray-200 lg:pl-8 pt-4 lg:pt-0">
            {!showUserInfoForm && !showCardInput && (
              <div className="text-center py-4">
                <h2 className="text-lg font-medium mb-4">Ready to Complete Your Order?</h2>
                <button
                  onClick={handleProceedToPayment}
                  disabled={cartTotal < 0.50 || cartItems.length === 0}
                  className={`w-full flex items-center justify-center py-3 px-4 rounded-md font-medium transition-colors mb-4 ${
                    cartTotal < 0.50 || cartItems.length === 0
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-[#575CEE] text-white hover:bg-[#4a4fd1]'
                  }`}
                >
                  <FaCreditCard className="mr-2" />
                  Proceed to Checkout
                </button>
              </div>
            )}

            {/* User Information Form */}
            {showUserInfoForm && !showCardInput && (
              <div className="mt-6 bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Shipping Information</h3>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700">Full Name</label>
                    <div className="mt-1 relative">
                      <FaUser className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={userInfo.name}
                        onChange={handleUserInfoChange}
                        className={`pl-10 block w-full border ${userInfoErrors.name ? 'border-red-300' : 'border-gray-300'} rounded-md shadow-sm focus:ring-[#575CEE] focus:border-[#575CEE] sm:text-sm`}
                      />
                      {userInfoErrors.name && <p className="mt-1 text-xs text-red-600">{userInfoErrors.name}</p>}
                    </div>
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700">Email Address</label>
                    <div className="mt-1 relative">
                      <FaEnvelope className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={userInfo.email}
                        onChange={handleUserInfoChange}
                        className={`pl-10 block w-full border ${userInfoErrors.email ? 'border-red-300' : 'border-gray-300'} rounded-md shadow-sm focus:ring-[#575CEE] focus:border-[#575CEE] sm:text-sm`}
                      />
                      {userInfoErrors.email && <p className="mt-1 text-xs text-red-600">{userInfoErrors.email}</p>}
                    </div>
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700">Phone Number</label>
                    <div className="mt-1 relative">
                      <FaPhone className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={userInfo.phone}
                        onChange={handleUserInfoChange}
                        className={`pl-10 block w-full border ${userInfoErrors.phone ? 'border-red-300' : 'border-gray-300'} rounded-md shadow-sm focus:ring-[#575CEE] focus:border-[#575CEE] sm:text-sm`}
                      />
                      {userInfoErrors.phone && <p className="mt-1 text-xs text-red-600">{userInfoErrors.phone}</p>}
                    </div>
                  </div>

                  <div>
                    <label htmlFor="address" className="block text-sm font-medium text-gray-700">Shipping Address</label>
                    <div className="mt-1 relative">
                      <FaMapMarkerAlt className="absolute left-3 top-3 text-gray-400" />
                      <textarea
                        id="address"
                        name="address"
                        rows="3"
                        value={userInfo.address}
                        onChange={handleUserInfoChange}
                        className={`pl-10 block w-full border ${userInfoErrors.address ? 'border-red-300' : 'border-gray-300'} rounded-md shadow-sm focus:ring-[#575CEE] focus:border-[#575CEE] sm:text-sm`}
                      />
                      {userInfoErrors.address && <p className="mt-1 text-xs text-red-600">{userInfoErrors.address}</p>}
                    </div>
                  </div>

                  <button
                    onClick={handleCheckout}
                    disabled={isProcessingPayment}
                    className="w-full bg-[#575CEE] text-white py-2 px-4 rounded-md hover:bg-[#4a4fd1] disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    {isProcessingPayment ? (
                      <>
                        <FaSpinner className="animate-spin mr-2" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <FaCreditCard className="mr-2" />
                        Continue to Payment
                      </>
                    )}
                  </button>
                </div>
              </div>
            )}

            {/* Card Input Component */}
            {showCardInput && clientSecret && (
              <div>
                <h2 className="text-lg font-medium mb-4">Payment Details</h2>
                <CardInput
                  clientSecret={clientSecret}
                  onPaymentSuccess={handlePaymentSuccess}
                  onPaymentError={handlePaymentError}
                />
              </div>
            )}

            {/* Payment Messages */}
            {paymentError && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-red-600 text-sm">{paymentError}</p>
              </div>
            )}

            {paymentSuccess && (
              <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-green-600 text-sm">
                  Payment successful!
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Back to Shopping Button */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <Link
            to="/home"
            className="w-full block text-center border border-gray-300 text-gray-700 py-3 px-4 rounded-md hover:bg-gray-50 transition-colors"
          >
            <FaArrowLeft className="inline mr-2" /> Continue Shopping
          </Link>
        </div>
      </div>

    </div>
  );
};

export default Cart;

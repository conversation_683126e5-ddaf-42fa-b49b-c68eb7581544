const CreateUser = require('../model/rawDataModel');
const Product = require('../model/productModel');
const Order = require('../model/orderModel');
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const jwt =  require('jsonwebtoken')
const upload = require('../Middleware/Multer');

const User = [{
    name: 'Atif',
    email: '<EMAIL>',
    password: '123456',

},
{
    name: '<PERSON>',
    email: '<EMAIL>',
    password: '123456',
},
{
    name: '<PERSON>',
    email: '<EMAIL>',
    password: '123456',
}
]

exports.GetAllUser = async (req, res) => {
    try {
        const alluser = await CreateUser.find();
        res.json({ user: alluser })
    } catch (e) {
        console.log(e);
    }
}


exports.UserData = async (req, res) => {
    console.log(req.params);

    let user = User.filter((user) => user.name == req.params.name);
    if (user.length > 0) {
        res.send({ msg: 'filter Data', data: user });
    } else {
        res.send({ msg: 'No Data Found' });
    }
}

exports.CreateNewUser = async (req, res) => {
    try {
        const { name, email, password } = req.body;
        console.log(name, email, password);

        const encryptPassword = await bcrypt.hash(password, 10);
        const newUser = await CreateUser.create({ name, email, password: encryptPassword });
   console.log(newUser);
        return res.status(201).json({ msg: 'User Created', data: newUser });

    } catch (e) {
        console.log(`Error is : ` + e);
    }

}

exports.deleteuser = async (req, res) => {
    try {
        const id = req.params.User_id;
        const deleteuser = await CreateUser.findByIdAndDelete(id)
        if (!deleteuser) {
            return res.status(404).json({ msg: 'User Not Found' });
        }
        return res.status(200).json({ msg: 'User Deleted', user: deleteuser });
    }
    catch (err) {
        console.log(err);

    }
}

exports.UpdateUser = async (req, res) => {
    try {
        const id = req.params.User_id;
        const Updateuser = await CreateUser.findByIdAndUpdate(id, req.body, { new: true });
        if (!Updateuser) {
            return res.status(404).json({ msg: 'User Not Found' });
        }
        return res.status(200).json({ msg: 'User Updated', NewUser: Updateuser });
    } catch (e) {
        console.log(e);
    }
}

exports.AuthRoute = async (req, res) => {
    try {
        const { email, password } = req.body;
        if (!email) {
            return res.status(400).json({ msg: 'Email is required' });
        }
        if (!password) {
            return res.status(400).json({ msg: 'password is required' });

        }
        const find_User = await CreateUser.findOne({ email })

        if (!find_User) {
            return res.status(400).json({ msg: 'user not found or invalid email' });
        }


        const matchPassword = await bcrypt.compare(password, find_User.password);
        if (!matchPassword) {
            return res.status(400).json({ msg: 'Invalid Password' });
        }
        const token = await jwt.sign({
             id: find_User._id,
             email: find_User.email,
              name: find_User.name
            },
            "axzsndhsj12343563-+}{\@#$%&*'/?",
             { expiresIn: '1d' });



        return res.status(200).json({ msg: 'User Found', user: find_User,Token:token });

    } catch (e) {
        console.log(e);
    }
}

exports.UloadFile = (req, res) => {
    try {

        const { file } = req.file;
        console.log(file);
        return res.status(200).json({ msg: 'File Uploaded' ,fileIs:file});
    } catch (e) {
        console.log(e);
    }
}

exports.createProduct = async (req, res) => {
    try {
        const { name, price, description, category, stock } = req.body;
        const image = req.file ? req.file.filename : null;
        const product = await Product.create({ name, price, description, image, category, stock });
        res.status(201).json({ success: true, product });
    } catch (e) {
        res.status(500).json({ success: false, message: e.message });
    }
};

exports.getAllProducts = async (req, res) => {
    try {
        const products = await Product.find();
        res.status(200).json({ success: true, products });
    } catch (e) {
        res.status(500).json({ success: false, message: e.message });
    }
};

// ==================== ORDER MANAGEMENT FUNCTIONS ====================

// Create new order
exports.createOrder = async (req, res) => {
    try {
        const {
            user,
            products,
            amount,
            paymentIntentId,
            customerInfo,
            shippingAddress,
            paymentStatus = 'succeeded'
        } = req.body;

        // Validate required fields
        if (!user || !products || !amount || !paymentIntentId || !customerInfo || !shippingAddress) {
            return res.status(400).json({
                success: false,
                message: 'Missing required fields: user, products, amount, paymentIntentId, customerInfo, shippingAddress'
            });
        }

        // Validate products array
        if (!Array.isArray(products) || products.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Products must be a non-empty array'
            });
        }

        // Check if order with this payment intent already exists
        const existingOrder = await Order.findOne({ paymentIntentId });
        if (existingOrder) {
            return res.status(400).json({
                success: false,
                message: 'Order with this payment intent already exists',
                order: existingOrder
            });
        }

        // Enhanced product validation and processing
        console.log('🔍 Processing products for order creation:', products);

        const populatedProducts = [];
        const mockProductsCreated = [];

        for (const item of products) {
            console.log(`🔍 Processing product item:`, item);

            let product = null;
            let isMockProduct = false;

            // Try to find product by MongoDB ObjectId first
            if (mongoose.Types.ObjectId.isValid(item.product)) {
                console.log(`✅ Valid ObjectId detected: ${item.product}`);
                try {
                    product = await Product.findById(item.product);
                    if (product) {
                        console.log(`✅ Found real product in database:`, product.name);
                    }
                } catch (error) {
                    console.log(`❌ Error finding product by ObjectId:`, error.message);
                }
            } else {
                console.log(`⚠️ Invalid ObjectId detected: ${item.product} (type: ${typeof item.product})`);
            }

            // If not found or invalid ObjectId, handle as mock/demo product
            if (!product) {
                console.log(`🔧 Creating mock product for ID: ${item.product}`);
                isMockProduct = true;

                // Create a temporary mock product that won't be saved to database
                const mockProduct = {
                    _id: new mongoose.Types.ObjectId(), // Generate a valid ObjectId for the order
                    name: item.name || `Product ${item.product}`,
                    price: item.price || 10000, // Default price in cents ($100)
                    category: 'demo',
                    originalId: item.product // Keep track of original ID
                };

                // Store mock product info for later reference
                mockProductsCreated.push(mockProduct);

                populatedProducts.push({
                    product: mockProduct._id,
                    name: mockProduct.name,
                    price: mockProduct.price,
                    quantity: item.quantity,
                    isMockProduct: true,
                    originalProductId: item.product
                });

                console.log(`✅ Created mock product entry:`, mockProduct);
            } else {
                // Use real product from database
                populatedProducts.push({
                    product: product._id,
                    name: product.name,
                    price: product.price,
                    quantity: item.quantity,
                    isMockProduct: false
                });
                console.log(`✅ Using real product:`, product.name);
            }
        }

        console.log(`📊 Product processing summary: ${populatedProducts.length} total, ${mockProductsCreated.length} mock products`);

        // Store mock products info for response
        const orderMetadata = {
            hasMockProducts: mockProductsCreated.length > 0,
            mockProducts: mockProductsCreated
        };

        // Calculate estimated delivery (7 days from now)
        const estimatedDelivery = new Date();
        estimatedDelivery.setDate(estimatedDelivery.getDate() + 7);

        // Create order
        console.log('📦 Creating order with processed products...');
        const order = await Order.create({
            user,
            products: populatedProducts,
            amount,
            paymentIntentId,
            paymentStatus,
            status: paymentStatus === 'succeeded' ? 'processing' : 'pending',
            customerInfo,
            shippingAddress,
            estimatedDelivery
        });

        console.log('✅ Order created successfully with ID:', order._id);

        // Smart populate operation that handles mock products
        let populatedOrder;

        if (orderMetadata.hasMockProducts) {
            console.log('🔧 Order contains mock products, using custom population...');

            // For orders with mock products, we'll manually construct the response
            // to avoid populate errors on non-existent products
            populatedOrder = await Order.findById(order._id).populate('user', 'name email');

            // Manually add product details for mock products
            populatedOrder = populatedOrder.toObject();
            populatedOrder.products = populatedOrder.products.map(orderProduct => {
                const mockProduct = mockProductsCreated.find(mock =>
                    mock._id.toString() === orderProduct.product.toString()
                );

                if (mockProduct) {
                    return {
                        ...orderProduct,
                        product: {
                            _id: mockProduct._id,
                            name: mockProduct.name,
                            price: mockProduct.price,
                            category: mockProduct.category,
                            originalId: mockProduct.originalId
                        }
                    };
                } else {
                    // This shouldn't happen, but just in case
                    return orderProduct;
                }
            });

            console.log('✅ Custom population completed for mock products');
        } else {
            console.log('✅ Order contains only real products, using standard population...');
            // Standard populate for real products only
            populatedOrder = await Order.findById(order._id)
                .populate('user', 'name email')
                .populate('products.product', 'name price category');
        }

        res.status(201).json({
            success: true,
            message: 'Order created successfully',
            order: populatedOrder,
            metadata: orderMetadata
        });

    } catch (error) {
        console.error('❌ Critical error in order creation:', error);
        console.error('❌ Error stack:', error.stack);

        // Provide detailed error information for debugging
        let errorMessage = 'Failed to create order';
        let errorDetails = error.message;

        if (error.message.includes('Cast to ObjectId failed')) {
            console.error('❌ ObjectId casting error detected - this should not happen with the new fix!');
            errorMessage = 'Product ID format error detected';
            errorDetails = 'There was an issue with product ID formatting. Please try again or contact support.';
        } else if (error.message.includes('validation failed')) {
            console.error('❌ Validation error:', error.message);
            errorMessage = 'Order validation failed';
        } else if (error.name === 'MongoError' || error.name === 'MongooseError') {
            console.error('❌ Database error:', error.message);
            errorMessage = 'Database operation failed';
        }

        res.status(500).json({
            success: false,
            message: errorMessage,
            error: errorDetails,
            timestamp: new Date().toISOString(),
            ...(process.env.NODE_ENV === 'development' && {
                fullError: error.message,
                stack: error.stack
            })
        });
    }
};

// Get all orders (admin only) with pagination and filtering
exports.getAllOrders = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            status,
            paymentStatus,
            startDate,
            endDate,
            search
        } = req.query;

        // Build filter object
        const filter = { isDeleted: false };

        if (status) filter.status = status;
        if (paymentStatus) filter.paymentStatus = paymentStatus;

        // Date range filter
        if (startDate || endDate) {
            filter.createdAt = {};
            if (startDate) filter.createdAt.$gte = new Date(startDate);
            if (endDate) filter.createdAt.$lte = new Date(endDate);
        }

        // Search filter (customer name, email, or order number)
        if (search) {
            filter.$or = [
                { 'customerInfo.name': { $regex: search, $options: 'i' } },
                { 'customerInfo.email': { $regex: search, $options: 'i' } },
                { orderNumber: { $regex: search, $options: 'i' } }
            ];
        }

        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);

        // Get orders with pagination
        const orders = await Order.find(filter)
            .populate('user', 'name email')
            .populate('products.product', 'name price category')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        // Get total count for pagination
        const totalOrders = await Order.countDocuments(filter);
        const totalPages = Math.ceil(totalOrders / parseInt(limit));

        res.status(200).json({
            success: true,
            orders,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalOrders,
                hasNextPage: parseInt(page) < totalPages,
                hasPrevPage: parseInt(page) > 1
            }
        });

    } catch (error) {
        console.error('Error fetching orders:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch orders',
            error: error.message
        });
    }
};

// Get orders for specific user
exports.getUserOrders = async (req, res) => {
    try {
        const { userId } = req.params;
        const { page = 1, limit = 10, status } = req.query;

        // Build filter
        const filter = { user: userId, isDeleted: false };
        if (status) filter.status = status;

        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);

        // Get user orders
        const orders = await Order.find(filter)
            .populate('products.product', 'name price category image')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        const totalOrders = await Order.countDocuments(filter);
        const totalPages = Math.ceil(totalOrders / parseInt(limit));

        res.status(200).json({
            success: true,
            orders,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalOrders,
                hasNextPage: parseInt(page) < totalPages,
                hasPrevPage: parseInt(page) > 1
            }
        });

    } catch (error) {
        console.error('Error fetching user orders:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch user orders',
            error: error.message
        });
    }
};

// Get single order details
exports.getOrderById = async (req, res) => {
    try {
        const { orderId } = req.params;

        const order = await Order.findOne({ _id: orderId, isDeleted: false })
            .populate('user', 'name email')
            .populate('products.product', 'name price category image description');

        if (!order) {
            return res.status(404).json({
                success: false,
                message: 'Order not found'
            });
        }

        res.status(200).json({
            success: true,
            order
        });

    } catch (error) {
        console.error('Error fetching order:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch order',
            error: error.message
        });
    }
};

// Update order status (admin only)
exports.updateOrderStatus = async (req, res) => {
    try {
        const { orderId } = req.params;
        const { status, trackingNumber, notes } = req.body;

        // Validate status
        const validStatuses = ['pending', 'processing', 'shipped', 'delivered', 'completed', 'canceled', 'failed'];
        if (!validStatuses.includes(status)) {
            return res.status(400).json({
                success: false,
                message: `Invalid status. Valid statuses are: ${validStatuses.join(', ')}`
            });
        }

        const updateData = { status };
        if (trackingNumber) updateData.trackingNumber = trackingNumber;
        if (notes) updateData.notes = notes;

        const order = await Order.findOneAndUpdate(
            { _id: orderId, isDeleted: false },
            updateData,
            { new: true }
        ).populate('user', 'name email')
         .populate('products.product', 'name price category');

        if (!order) {
            return res.status(404).json({
                success: false,
                message: 'Order not found'
            });
        }

        res.status(200).json({
            success: true,
            message: 'Order status updated successfully',
            order
        });

    } catch (error) {
        console.error('Error updating order status:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update order status',
            error: error.message
        });
    }
};

// Soft delete order (admin only)
exports.deleteOrder = async (req, res) => {
    try {
        const { orderId } = req.params;

        const order = await Order.findOneAndUpdate(
            { _id: orderId, isDeleted: false },
            { isDeleted: true },
            { new: true }
        );

        if (!order) {
            return res.status(404).json({
                success: false,
                message: 'Order not found'
            });
        }

        res.status(200).json({
            success: true,
            message: 'Order deleted successfully'
        });

    } catch (error) {
        console.error('Error deleting order:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete order',
            error: error.message
        });
    }
};

// Get order statistics (admin dashboard)
exports.getOrderStats = async (req, res) => {
    try {
        const totalOrders = await Order.countDocuments({ isDeleted: false });
        const pendingOrders = await Order.countDocuments({ status: 'pending', isDeleted: false });
        const completedOrders = await Order.countDocuments({ status: 'completed', isDeleted: false });
        const processingOrders = await Order.countDocuments({ status: 'processing', isDeleted: false });

        // Calculate total revenue
        const revenueResult = await Order.aggregate([
            { $match: { paymentStatus: 'succeeded', isDeleted: false } },
            { $group: { _id: null, totalRevenue: { $sum: '$amount' } } }
        ]);
        const totalRevenue = revenueResult.length > 0 ? revenueResult[0].totalRevenue : 0;

        // Get recent orders
        const recentOrders = await Order.find({ isDeleted: false })
            .populate('user', 'name email')
            .populate('products.product', 'name price')
            .sort({ createdAt: -1 })
            .limit(5);

        res.status(200).json({
            success: true,
            stats: {
                totalOrders,
                pendingOrders,
                completedOrders,
                processingOrders,
                totalRevenue: totalRevenue / 100, // Convert from cents to dollars
                recentOrders
            }
        });

    } catch (error) {
        console.error('Error fetching order stats:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch order statistics',
            error: error.message
        });
    }
};

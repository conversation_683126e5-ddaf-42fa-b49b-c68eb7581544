const CreateUser = require('../model/rawDataModel');
const Product = require('../model/productModel');
const Order = require('../model/orderModel');
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const jwt =  require('jsonwebtoken')
const upload = require('../Middleware/Multer');

const User = [{
    name: 'Atif',
    email: '<EMAIL>',
    password: '123456',

},
{
    name: '<PERSON>',
    email: '<EMAIL>',
    password: '123456',
},
{
    name: '<PERSON>',
    email: '<EMAIL>',
    password: '123456',
}
]

exports.GetAllUser = async (req, res) => {
    try {
        const alluser = await CreateUser.find();
        res.json({ user: alluser })
    } catch (e) {
        console.log(e);
    }
}


exports.UserData = async (req, res) => {
    console.log(req.params);

    let user = User.filter((user) => user.name == req.params.name);
    if (user.length > 0) {
        res.send({ msg: 'filter Data', data: user });
    } else {
        res.send({ msg: 'No Data Found' });
    }
}

exports.CreateNewUser = async (req, res) => {
    try {
        const { name, email, password } = req.body;
        console.log(name, email, password);

        const encryptPassword = await bcrypt.hash(password, 10);
        const newUser = await CreateUser.create({ name, email, password: encryptPassword });
   console.log(newUser);
        return res.status(201).json({ msg: 'User Created', data: newUser });

    } catch (e) {
        console.log(`Error is : ` + e);
    }

}

exports.deleteuser = async (req, res) => {
    try {
        const id = req.params.User_id;
        const deleteuser = await CreateUser.findByIdAndDelete(id)
        if (!deleteuser) {
            return res.status(404).json({ msg: 'User Not Found' });
        }
        return res.status(200).json({ msg: 'User Deleted', user: deleteuser });
    }
    catch (err) {
        console.log(err);

    }
}

exports.UpdateUser = async (req, res) => {
    try {
        const id = req.params.User_id;
        const Updateuser = await CreateUser.findByIdAndUpdate(id, req.body, { new: true });
        if (!Updateuser) {
            return res.status(404).json({ msg: 'User Not Found' });
        }
        return res.status(200).json({ msg: 'User Updated', NewUser: Updateuser });
    } catch (e) {
        console.log(e);
    }
}

exports.AuthRoute = async (req, res) => {
    try {
        const { email, password } = req.body;
        if (!email) {
            return res.status(400).json({ msg: 'Email is required' });
        }
        if (!password) {
            return res.status(400).json({ msg: 'password is required' });

        }
        const find_User = await CreateUser.findOne({ email })

        if (!find_User) {
            return res.status(400).json({ msg: 'user not found or invalid email' });
        }


        const matchPassword = await bcrypt.compare(password, find_User.password);
        if (!matchPassword) {
            return res.status(400).json({ msg: 'Invalid Password' });
        }
        const token = await jwt.sign({
             id: find_User._id,
             email: find_User.email,
              name: find_User.name
            },
            "axzsndhsj12343563-+}{\@#$%&*'/?",
             { expiresIn: '1d' });



        return res.status(200).json({ msg: 'User Found', user: find_User,Token:token });

    } catch (e) {
        console.log(e);
    }
}

exports.UloadFile = (req, res) => {
    try {

        const { file } = req.file;
        console.log(file);
        return res.status(200).json({ msg: 'File Uploaded' ,fileIs:file});
    } catch (e) {
        console.log(e);
    }
}

// Note: createProduct and getAllProducts functions are now implemented
// in the enhanced product management section below with full CRUD operations

// ==================== ORDER MANAGEMENT FUNCTIONS ====================

// Create new order
exports.createOrder = async (req, res) => {
    try {
        const {
            user,
            products,
            amount,
            paymentIntentId,
            customerInfo,
            shippingAddress,
            paymentStatus = 'succeeded'
        } = req.body;

        // Validate required fields
        if (!user || !products || !amount || !paymentIntentId || !customerInfo || !shippingAddress) {
            return res.status(400).json({
                success: false,
                message: 'Missing required fields: user, products, amount, paymentIntentId, customerInfo, shippingAddress'
            });
        }

        // Validate products array
        if (!Array.isArray(products) || products.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Products must be a non-empty array'
            });
        }

        // Check if order with this payment intent already exists
        const existingOrder = await Order.findOne({ paymentIntentId });
        if (existingOrder) {
            return res.status(400).json({
                success: false,
                message: 'Order with this payment intent already exists',
                order: existingOrder
            });
        }

        // Enhanced product validation and processing
        console.log('🔍 Processing products for order creation:', products);

        const populatedProducts = [];
        const mockProductsCreated = [];

        for (const item of products) {
            console.log(`🔍 Processing product item:`, item);

            let product = null;
            let isMockProduct = false;

            // Try to find product by MongoDB ObjectId first
            if (mongoose.Types.ObjectId.isValid(item.product)) {
                console.log(`✅ Valid ObjectId detected: ${item.product}`);
                try {
                    product = await Product.findById(item.product);
                    if (product) {
                        console.log(`✅ Found real product in database:`, product.name);
                    }
                } catch (error) {
                    console.log(`❌ Error finding product by ObjectId:`, error.message);
                }
            } else {
                console.log(`⚠️ Invalid ObjectId detected: ${item.product} (type: ${typeof item.product})`);
            }

            // If not found or invalid ObjectId, handle as mock/demo product
            if (!product) {
                console.log(`🔧 Creating mock product for ID: ${item.product}`);
                isMockProduct = true;

                // Create a temporary mock product that won't be saved to database
                const mockProduct = {
                    _id: new mongoose.Types.ObjectId(), // Generate a valid ObjectId for the order
                    name: item.name || `Product ${item.product}`,
                    price: item.price || 10000, // Default price in cents ($100)
                    category: 'demo',
                    originalId: item.product // Keep track of original ID
                };

                // Store mock product info for later reference
                mockProductsCreated.push(mockProduct);

                populatedProducts.push({
                    product: mockProduct._id,
                    name: mockProduct.name,
                    price: mockProduct.price,
                    quantity: item.quantity,
                    isMockProduct: true,
                    originalProductId: item.product
                });

                console.log(`✅ Created mock product entry:`, mockProduct);
            } else {
                // Use real product from database
                populatedProducts.push({
                    product: product._id,
                    name: product.name,
                    price: product.price,
                    quantity: item.quantity,
                    isMockProduct: false
                });
                console.log(`✅ Using real product:`, product.name);
            }
        }

        console.log(`📊 Product processing summary: ${populatedProducts.length} total, ${mockProductsCreated.length} mock products`);

        // Store mock products info for response
        const orderMetadata = {
            hasMockProducts: mockProductsCreated.length > 0,
            mockProducts: mockProductsCreated
        };

        // Calculate estimated delivery (7 days from now)
        const estimatedDelivery = new Date();
        estimatedDelivery.setDate(estimatedDelivery.getDate() + 7);

        // Generate order number if not provided (backup method)
        let orderNumber;
        try {
            const count = await Order.countDocuments({});
            const timestamp = Date.now().toString().slice(-6);
            const orderCount = (count + 1).toString().padStart(4, '0');
            orderNumber = `PW${timestamp}${orderCount}`;
            console.log(`🔢 Pre-generated order number: ${orderNumber}`);
        } catch (error) {
            console.error('❌ Error pre-generating order number:', error);
            orderNumber = `PW${Date.now().toString().slice(-6)}${Math.floor(Math.random() * 9999).toString().padStart(4, '0')}`;
            console.log(`🔧 Fallback order number: ${orderNumber}`);
        }

        // Create order
        console.log('📦 Creating order with processed products...');
        const orderData = {
            user,
            products: populatedProducts,
            amount,
            paymentIntentId,
            paymentStatus,
            status: paymentStatus === 'succeeded' ? 'processing' : 'pending',
            customerInfo,
            shippingAddress,
            estimatedDelivery,
            orderNumber // Explicitly include order number
        };

        console.log('📋 Order data being created:', {
            ...orderData,
            products: orderData.products.length + ' products'
        });

        const order = await Order.create(orderData);

        console.log('✅ Order created successfully with ID:', order._id);
        console.log('✅ Order number assigned:', order.orderNumber);

        // Verify order was created with all required fields
        if (!order.orderNumber) {
            console.error('❌ Warning: Order created without order number!');
            // Try to update the order with a generated number
            const fallbackOrderNumber = `PW${Date.now().toString().slice(-6)}${Math.floor(Math.random() * 9999).toString().padStart(4, '0')}`;
            await Order.findByIdAndUpdate(order._id, { orderNumber: fallbackOrderNumber });
            order.orderNumber = fallbackOrderNumber;
            console.log(`🔧 Updated order with fallback number: ${fallbackOrderNumber}`);
        }

        // Smart populate operation that handles mock products
        let populatedOrder;

        if (orderMetadata.hasMockProducts) {
            console.log('🔧 Order contains mock products, using custom population...');

            // For orders with mock products, we'll manually construct the response
            // to avoid populate errors on non-existent products
            populatedOrder = await Order.findById(order._id).populate('user', 'name email');

            // Manually add product details for mock products
            populatedOrder = populatedOrder.toObject();
            populatedOrder.products = populatedOrder.products.map(orderProduct => {
                const mockProduct = mockProductsCreated.find(mock =>
                    mock._id.toString() === orderProduct.product.toString()
                );

                if (mockProduct) {
                    return {
                        ...orderProduct,
                        product: {
                            _id: mockProduct._id,
                            name: mockProduct.name,
                            price: mockProduct.price,
                            category: mockProduct.category,
                            originalId: mockProduct.originalId
                        }
                    };
                } else {
                    // This shouldn't happen, but just in case
                    return orderProduct;
                }
            });

            console.log('✅ Custom population completed for mock products');
        } else {
            console.log('✅ Order contains only real products, using standard population...');
            // Standard populate for real products only
            populatedOrder = await Order.findById(order._id)
                .populate('user', 'name email')
                .populate('products.product', 'name price category');
        }

        res.status(201).json({
            success: true,
            message: 'Order created successfully',
            order: populatedOrder,
            metadata: orderMetadata
        });

    } catch (error) {
        console.error('❌ Critical error in order creation:', error);
        console.error('❌ Error stack:', error.stack);

        // Provide detailed error information for debugging
        let errorMessage = 'Failed to create order';
        let errorDetails = error.message;

        if (error.message.includes('Cast to ObjectId failed')) {
            console.error('❌ ObjectId casting error detected - this should not happen with the new fix!');
            errorMessage = 'Product ID format error detected';
            errorDetails = 'There was an issue with product ID formatting. Please try again or contact support.';
        } else if (error.message.includes('validation failed')) {
            console.error('❌ Validation error:', error.message);
            errorMessage = 'Order validation failed';
        } else if (error.name === 'MongoError' || error.name === 'MongooseError') {
            console.error('❌ Database error:', error.message);
            errorMessage = 'Database operation failed';
        }

        res.status(500).json({
            success: false,
            message: errorMessage,
            error: errorDetails,
            timestamp: new Date().toISOString(),
            ...(process.env.NODE_ENV === 'development' && {
                fullError: error.message,
                stack: error.stack
            })
        });
    }
};

// Get all orders (admin only) with pagination and filtering
exports.getAllOrders = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            status,
            paymentStatus,
            startDate,
            endDate,
            search
        } = req.query;

        // Build filter object
        const filter = { isDeleted: false };

        if (status) filter.status = status;
        if (paymentStatus) filter.paymentStatus = paymentStatus;

        // Date range filter
        if (startDate || endDate) {
            filter.createdAt = {};
            if (startDate) filter.createdAt.$gte = new Date(startDate);
            if (endDate) filter.createdAt.$lte = new Date(endDate);
        }

        // Search filter (customer name, email, or order number)
        if (search) {
            filter.$or = [
                { 'customerInfo.name': { $regex: search, $options: 'i' } },
                { 'customerInfo.email': { $regex: search, $options: 'i' } },
                { orderNumber: { $regex: search, $options: 'i' } }
            ];
        }

        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);

        // Get orders with pagination
        let orders = await Order.find(filter)
            .populate('user', 'name email')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        // Enhanced population handling for both real and mock products
        orders = await Promise.all(orders.map(async (order) => {
            const orderObj = order.toObject();

            // Process products to handle both real and mock products
            if (orderObj.products && orderObj.products.length > 0) {
                orderObj.products = await Promise.all(orderObj.products.map(async (item) => {
                    // Try to populate real product
                    if (mongoose.Types.ObjectId.isValid(item.product)) {
                        try {
                            const populatedProduct = await Product.findById(item.product);
                            if (populatedProduct) {
                                return {
                                    ...item,
                                    product: {
                                        _id: populatedProduct._id,
                                        name: populatedProduct.name,
                                        price: populatedProduct.price,
                                        category: populatedProduct.category
                                    }
                                };
                            }
                        } catch (error) {
                            console.log('Product not found in database, using stored data');
                        }
                    }

                    // Use stored product data (for mock products or when real product not found)
                    return {
                        ...item,
                        product: item.product || { name: item.name, price: item.price }
                    };
                }));
            }

            return orderObj;
        }));

        // Get total count for pagination
        const totalOrders = await Order.countDocuments(filter);
        const totalPages = Math.ceil(totalOrders / parseInt(limit));

        res.status(200).json({
            success: true,
            orders,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalOrders,
                hasNextPage: parseInt(page) < totalPages,
                hasPrevPage: parseInt(page) > 1
            }
        });

    } catch (error) {
        console.error('Error fetching orders:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch orders',
            error: error.message
        });
    }
};

// Get orders for specific user
exports.getUserOrders = async (req, res) => {
    try {
        const { userId } = req.params;
        const { page = 1, limit = 10, status } = req.query;

        // Build filter
        const filter = { user: userId, isDeleted: false };
        if (status) filter.status = status;

        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);

        // Get user orders with enhanced product population
        let orders = await Order.find(filter)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        // Enhanced population handling for both real and mock products
        orders = await Promise.all(orders.map(async (order) => {
            const orderObj = order.toObject();

            // Process products to handle both real and mock products
            if (orderObj.products && orderObj.products.length > 0) {
                orderObj.products = await Promise.all(orderObj.products.map(async (item) => {
                    // Try to populate real product
                    if (mongoose.Types.ObjectId.isValid(item.product)) {
                        try {
                            const populatedProduct = await Product.findById(item.product);
                            if (populatedProduct) {
                                return {
                                    ...item,
                                    product: {
                                        _id: populatedProduct._id,
                                        name: populatedProduct.name,
                                        price: populatedProduct.price,
                                        category: populatedProduct.category,
                                        image: populatedProduct.image
                                    }
                                };
                            }
                        } catch (error) {
                            console.log('Product not found in database, using stored data');
                        }
                    }

                    // Use stored product data (for mock products or when real product not found)
                    return {
                        ...item,
                        product: item.product || { name: item.name, price: item.price }
                    };
                }));
            }

            return orderObj;
        }));

        const totalOrders = await Order.countDocuments(filter);
        const totalPages = Math.ceil(totalOrders / parseInt(limit));

        res.status(200).json({
            success: true,
            orders,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalOrders,
                hasNextPage: parseInt(page) < totalPages,
                hasPrevPage: parseInt(page) > 1
            }
        });

    } catch (error) {
        console.error('Error fetching user orders:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch user orders',
            error: error.message
        });
    }
};

// Get single order details
exports.getOrderById = async (req, res) => {
    try {
        const { orderId } = req.params;

        const order = await Order.findOne({ _id: orderId, isDeleted: false })
            .populate('user', 'name email')
            .populate('products.product', 'name price category image description');

        if (!order) {
            return res.status(404).json({
                success: false,
                message: 'Order not found'
            });
        }

        res.status(200).json({
            success: true,
            order
        });

    } catch (error) {
        console.error('Error fetching order:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch order',
            error: error.message
        });
    }
};

// Update order status (admin only)
exports.updateOrderStatus = async (req, res) => {
    try {
        const { orderId } = req.params;
        const { status, trackingNumber, notes } = req.body;

        // Validate status
        const validStatuses = ['pending', 'processing', 'shipped', 'delivered', 'completed', 'canceled', 'failed'];
        if (!validStatuses.includes(status)) {
            return res.status(400).json({
                success: false,
                message: `Invalid status. Valid statuses are: ${validStatuses.join(', ')}`
            });
        }

        const updateData = { status };
        if (trackingNumber) updateData.trackingNumber = trackingNumber;
        if (notes) updateData.notes = notes;

        const order = await Order.findOneAndUpdate(
            { _id: orderId, isDeleted: false },
            updateData,
            { new: true }
        ).populate('user', 'name email')
         .populate('products.product', 'name price category');

        if (!order) {
            return res.status(404).json({
                success: false,
                message: 'Order not found'
            });
        }

        res.status(200).json({
            success: true,
            message: 'Order status updated successfully',
            order
        });

    } catch (error) {
        console.error('Error updating order status:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update order status',
            error: error.message
        });
    }
};

// Soft delete order (admin only)
exports.deleteOrder = async (req, res) => {
    try {
        const { orderId } = req.params;

        const order = await Order.findOneAndUpdate(
            { _id: orderId, isDeleted: false },
            { isDeleted: true },
            { new: true }
        );

        if (!order) {
            return res.status(404).json({
                success: false,
                message: 'Order not found'
            });
        }

        res.status(200).json({
            success: true,
            message: 'Order deleted successfully'
        });

    } catch (error) {
        console.error('Error deleting order:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete order',
            error: error.message
        });
    }
};

// Get order statistics (admin dashboard)
exports.getOrderStats = async (req, res) => {
    try {
        const totalOrders = await Order.countDocuments({ isDeleted: false });
        const pendingOrders = await Order.countDocuments({ status: 'pending', isDeleted: false });
        const completedOrders = await Order.countDocuments({ status: 'completed', isDeleted: false });
        const processingOrders = await Order.countDocuments({ status: 'processing', isDeleted: false });

        // Calculate total revenue
        const revenueResult = await Order.aggregate([
            { $match: { paymentStatus: 'succeeded', isDeleted: false } },
            { $group: { _id: null, totalRevenue: { $sum: '$amount' } } }
        ]);
        const totalRevenue = revenueResult.length > 0 ? revenueResult[0].totalRevenue : 0;

        // Get recent orders
        const recentOrders = await Order.find({ isDeleted: false })
            .populate('user', 'name email')
            .populate('products.product', 'name price')
            .sort({ createdAt: -1 })
            .limit(5);

        res.status(200).json({
            success: true,
            stats: {
                totalOrders,
                pendingOrders,
                completedOrders,
                processingOrders,
                totalRevenue: totalRevenue / 100, // Convert from cents to dollars
                recentOrders
            }
        });

    } catch (error) {
        console.error('Error fetching order stats:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch order statistics',
            error: error.message
        });
    }
};

// ==================== PRODUCT MANAGEMENT APIs ====================

// Get all products (with admin options)
exports.getAllProducts = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            category,
            search,
            includeDeleted = false,
            status
        } = req.query;

        console.log('📦 Getting all products with filters:', { page, limit, category, search, includeDeleted, status });

        // Build filter object
        let filter = {};

        // Include/exclude deleted products
        if (includeDeleted === 'true') {
            // Admin view - include all products
        } else {
            filter.isDeleted = false;
        }

        // Category filter
        if (category && category !== 'all') {
            filter.category = category;
        }

        // Search filter
        if (search) {
            filter.$or = [
                { name: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } }
            ];
        }

        // Status filter
        if (status && status !== 'all') {
            switch (status) {
                case 'in_stock':
                    filter.stock = { $gte: 10 };
                    filter.isDeleted = false;
                    break;
                case 'low_stock':
                    filter.stock = { $gt: 0, $lt: 10 };
                    filter.isDeleted = false;
                    break;
                case 'out_of_stock':
                    filter.stock = 0;
                    filter.isDeleted = false;
                    break;
                case 'deleted':
                    filter.isDeleted = true;
                    break;
            }
        }

        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);

        // Get products with pagination
        const products = await Product.find(filter)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        // Get total count for pagination
        const totalProducts = await Product.countDocuments(filter);
        const totalPages = Math.ceil(totalProducts / parseInt(limit));

        console.log(`✅ Found ${products.length} products (${totalProducts} total)`);

        res.status(200).json({
            success: true,
            products,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalProducts,
                hasNextPage: parseInt(page) < totalPages,
                hasPrevPage: parseInt(page) > 1
            }
        });

    } catch (error) {
        console.error('❌ Error getting products:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve products',
            error: error.message
        });
    }
};

// Get single product by ID
exports.getProductById = async (req, res) => {
    try {
        const { productId } = req.params;
        console.log('📦 Getting product by ID:', productId);

        if (!mongoose.Types.ObjectId.isValid(productId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid product ID format'
            });
        }

        const product = await Product.findById(productId);

        if (!product) {
            return res.status(404).json({
                success: false,
                message: 'Product not found'
            });
        }

        console.log('✅ Product found:', product.name);

        res.status(200).json({
            success: true,
            product
        });

    } catch (error) {
        console.error('❌ Error getting product:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve product',
            error: error.message
        });
    }
};

// Create new product
exports.createProduct = async (req, res) => {
    try {
        const { name, price, description, category, stock, featured } = req.body;
        console.log('📦 Creating new product:', { name, price, category, stock, featured });
        console.log('📷 Uploaded file:', req.file);

        // Validate required fields
        if (!name || !price || !description || !category) {
            return res.status(400).json({
                success: false,
                message: 'Missing required fields: name, price, description, category'
            });
        }

        // Handle image upload
        let imagePath = '';
        if (req.file) {
            // Store the relative path that can be accessed via /images endpoint
            imagePath = req.file.filename;
            console.log('✅ Image uploaded:', imagePath);
        } else {
            console.log('⚠️ No image uploaded');
        }

        // Create product
        const product = await Product.create({
            name: name.trim(),
            price: parseFloat(price),
            description: description.trim(),
            image: imagePath,
            category,
            stock: parseInt(stock) || 0,
            featured: featured === 'true' || featured === true
        });

        console.log('✅ Product created successfully:', product._id);

        res.status(201).json({
            success: true,
            message: 'Product created successfully',
            product
        });

    } catch (error) {
        console.error('❌ Error creating product:', error);

        if (error.name === 'ValidationError') {
            const validationErrors = Object.values(error.errors).map(err => err.message);
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: validationErrors
            });
        }

        res.status(500).json({
            success: false,
            message: 'Failed to create product',
            error: error.message
        });
    }
};

// Update product
exports.updateProduct = async (req, res) => {
    try {
        const { productId } = req.params;
        const { name, price, description, category, stock, featured } = req.body;

        console.log('📦 Updating product:', productId, { name, price, category, stock, featured });
        console.log('📷 Uploaded file:', req.file);

        // Validate product ID
        if (!mongoose.Types.ObjectId.isValid(productId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid product ID format'
            });
        }

        // Find the product
        const product = await Product.findOne({ _id: productId, isDeleted: false });
        if (!product) {
            return res.status(404).json({
                success: false,
                message: 'Product not found'
            });
        }

        // Prepare update data
        const updateData = {};
        if (name !== undefined) updateData.name = name.trim();
        if (price !== undefined) updateData.price = parseFloat(price);
        if (description !== undefined) updateData.description = description.trim();
        if (category !== undefined) updateData.category = category;
        if (stock !== undefined) updateData.stock = parseInt(stock);
        if (featured !== undefined) updateData.featured = featured === 'true' || featured === true;

        // Handle image upload
        if (req.file) {
            updateData.image = req.file.filename;
            console.log('✅ New image uploaded:', req.file.filename);
        }

        // Update the product
        const updatedProduct = await Product.findByIdAndUpdate(
            productId,
            updateData,
            { new: true, runValidators: true }
        );

        console.log('✅ Product updated successfully:', updatedProduct.name);

        res.status(200).json({
            success: true,
            message: 'Product updated successfully',
            product: updatedProduct
        });

    } catch (error) {
        console.error('❌ Error updating product:', error);

        if (error.name === 'ValidationError') {
            const validationErrors = Object.values(error.errors).map(err => err.message);
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: validationErrors
            });
        }

        res.status(500).json({
            success: false,
            message: 'Failed to update product',
            error: error.message
        });
    }
};

// Delete product (soft delete)
exports.deleteProduct = async (req, res) => {
    try {
        const { productId } = req.params;
        console.log('📦 Deleting product:', productId);

        // Validate product ID
        if (!mongoose.Types.ObjectId.isValid(productId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid product ID format'
            });
        }

        // Find the product
        const product = await Product.findOne({ _id: productId, isDeleted: false });
        if (!product) {
            return res.status(404).json({
                success: false,
                message: 'Product not found'
            });
        }

        // Check if product is referenced in any orders
        const ordersWithProduct = await Order.countDocuments({
            'products.product': productId,
            isDeleted: false
        });

        if (ordersWithProduct > 0) {
            console.log(`⚠️ Product ${productId} is referenced in ${ordersWithProduct} orders`);
            return res.status(400).json({
                success: false,
                message: `Cannot delete product. It is referenced in ${ordersWithProduct} existing order(s).`,
                referencedOrders: ordersWithProduct
            });
        }

        // Perform soft delete
        await product.softDelete();

        console.log('✅ Product soft deleted successfully:', product.name);

        res.status(200).json({
            success: true,
            message: 'Product deleted successfully'
        });

    } catch (error) {
        console.error('❌ Error deleting product:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete product',
            error: error.message
        });
    }
};

// Get product statistics (admin dashboard)
exports.getProductStats = async (req, res) => {
    try {
        const totalProducts = await Product.countDocuments({ isDeleted: false });
        const inStockProducts = await Product.countDocuments({ stock: { $gte: 10 }, isDeleted: false });
        const lowStockProducts = await Product.countDocuments({ stock: { $gt: 0, $lt: 10 }, isDeleted: false });
        const outOfStockProducts = await Product.countDocuments({ stock: 0, isDeleted: false });
        const deletedProducts = await Product.countDocuments({ isDeleted: true });

        // Get products by category
        const productsByCategory = await Product.aggregate([
            { $match: { isDeleted: false } },
            { $group: { _id: '$category', count: { $sum: 1 } } },
            { $sort: { count: -1 } }
        ]);

        // Get recent products
        const recentProducts = await Product.find({ isDeleted: false })
            .sort({ createdAt: -1 })
            .limit(5)
            .select('name price category stock createdAt');

        res.status(200).json({
            success: true,
            stats: {
                totalProducts,
                inStockProducts,
                lowStockProducts,
                outOfStockProducts,
                deletedProducts,
                productsByCategory,
                recentProducts
            }
        });

    } catch (error) {
        console.error('Error fetching product stats:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch product statistics',
            error: error.message
        });
    }
};

const mongoose = require('mongoose');

const OrderSchema = new mongoose.Schema({
    products: [{
        product: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Product',
            required: [true, 'Product is required']
        },
        quantity: {
            type: Number,
            required: [true, 'Quantity is required'],
            min: [1, 'Quantity must be at least 1']
        }
    }],
    amount: {
        type: Number,
        required: [true, 'Amount is required'],
        min: [0, 'Amount cannot be negative']
    },
    paymentIntentId: {
        type: String,
        required: [true, 'Payment Intent ID is required'],
        unique: true
    },
    status: {
        type: String,
        enum: {
            values: ['pending', 'completed', 'failed'],
            message: '{VALUE} is not a valid status'
        },
        default: 'pending'
    },
    customerInfo: {
        name: String,
        email: String,
        phone: String
    },
    shippingAddress: {
        address: String,
        city: String,
        postalCode: String,
        country: String
    },
    orderDate: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true
});

// Define indexes
OrderSchema.index({ user: 1, createdAt: -1 });

module.exports = mongoose.model('Order', OrderSchema);